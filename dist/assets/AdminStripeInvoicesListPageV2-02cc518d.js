import{j as e}from"./@nivo/heatmap-ba1ecfff.js";import{b as a,f as Z}from"./vendor-851db8c1.js";import{w as ee,M as te,G as se,A as ae,B as re,c as ie,t as ne,g as oe}from"./index-12adfaa3.js";import{o as ce}from"./yup-2824f222.js";import{u as le}from"./react-hook-form-687afde5.js";import{c as de,a as pe}from"./yup-54691517.js";import{P as me}from"./index-eb1bc208.js";import{A as ue,a as xe}from"./index.esm-9c6194ba.js";import{a as he}from"./index.esm-c561e951.js";import{S as ge}from"./index-02625b16.js";import"./@craftjs/core-d3c11b68.js";import"./@fortawesome/react-fontawesome-13437837.js";import"./@fortawesome/fontawesome-svg-core-4fa3e289.js";import"./react-confirm-alert-cd7ccfe7.js";import"./@tanstack/react-query-20158223.js";import"./@stripe/stripe-js-6b714a86.js";import"./moment-a9aaa855.js";import"./cal-heatmap-cf010ec4.js";import"./react-icons-51bc3cff.js";import"./smoothscroll-polyfill-a5c0a116.js";import"./date-fns-07266b7d.js";import"./lodash-91d5d207.js";import"./numeral-ea653b2a.js";import"./@stripe/react-stripe-js-64f0e61f.js";import"./react-select-c8303602.js";import"./@mantine/core-8cbffb6d.js";import"./@emotion/react-89b506c3.js";import"./@emotion/cache-9a5b99cd.js";import"./@emotion/utils-8a8f62c5.js";import"./@emotion/serialize-460cad7f.js";import"./@uppy/dashboard-4a19149e.js";import"./@fullcalendar/core-8ccc1ac4.js";import"./@uppy/core-0760343f.js";import"./@uppy/aws-s3-c5961f7a.js";import"./@uppy/compressor-11f993e4.js";import"./@headlessui/react-a5400090.js";import"./@fortawesome/free-solid-svg-icons-0a9c4907.js";import"./@fortawesome/free-regular-svg-icons-0a88e957.js";import"./@fortawesome/free-brands-svg-icons-fae0dcac.js";import"./@hookform/resolvers-67648cca.js";const f=[{header:"Status",accessor:"status"},{header:"Currency",accessor:"currency"},{header:"Amount due",accessor:"amount_due",type:"currency"},{header:"Amount paid",accessor:"amount_paid",type:"currency"},{header:"Amount remaining",accessor:"amount_remaining",type:"currency"},{header:"Created at",accessor:"created_at",type:"timestamp"}],fe=()=>{const E=new te,{dispatch:y}=a.useContext(se),{dispatch:_}=a.useContext(ae);a.useState("");const[$,L]=a.useState([]),[o,j]=a.useState(10),[w,D]=a.useState(0),[ye,O]=a.useState(0),[c,R]=a.useState(0),[T,q]=a.useState(!1),[I,z]=a.useState(!1),[v,b]=a.useState(!1),[N,S]=a.useState(!1),[n,p]=a.useState([]),[C,m]=a.useState([]),[B,V]=a.useState(""),[P,G]=a.useState("eq"),[M,A]=a.useState(!1);Z();const u=a.useRef(null),H=de({customer_email:pe()}),{register:je,handleSubmit:K,formState:{errors:we}}=le({resolver:ce(H)}),k=(t,r,s)=>{const i=r==="eq"&&isNaN(s)?`"${s}"`:s,x=`${t},${r},${i}`;m(h=>[...h.filter(d=>!d.includes(t)),x]),V(s)};function U(t){(async function(){j(t),await l(1,t)})()}function Y(){(async function(){await l(c-1>1?c-1:1,o)})()}function J(){(async function(){await l(c+1<=w?c+1:1,o)})()}async function l(t,r,s){A(!0);try{const{list:i,total:x,limit:h,num_pages:g,page:d,error:W,message:X}=await E.getStripeInvoicesV2({page:t,limit:r},`filter=${s.toString()}`);if(W){showToast(y,X,5e3);return}L(i),j(+h),D(+g),R(+d),O(+x),q(+d>1),z(+d+1<=+g)}catch(i){console.log("ERROR",i),ne(_,i.message)}A(!1)}const Q=t=>{const r=oe(t.customer_email);l(1,o,{customer_email:r,product_name})};a.useEffect(()=>{y({type:"SETPATH",payload:{path:"invoices"}});const r=setTimeout(async()=>{await l(1,o,C)},700);return()=>{clearTimeout(r)}},[B,C,P]);const F=t=>{u.current&&!u.current.contains(t.target)&&b(!1)};return a.useEffect(()=>(document.addEventListener("mousedown",F),()=>{document.removeEventListener("mousedown",F)}),[]),e.jsxs(e.Fragment,{children:[e.jsx("div",{className:"flex items-center justify-between py-3",children:e.jsx("form",{className:"relative rounded bg-white",onSubmit:K(Q),children:e.jsxs("div",{className:"flex items-center gap-4 text-nowrap text-gray-700",children:[e.jsxs("div",{className:"relative",ref:u,children:[e.jsxs("div",{className:"flex cursor-pointer items-center justify-between gap-3 rounded-md border border-gray-200 px-3 py-1",onClick:()=>b(!v),children:[e.jsx(re,{}),e.jsx("span",{children:"Filters"}),n.length>0&&e.jsx("span",{className:"flex h-6 w-6 items-center justify-center rounded-full bg-gray-800 text-start text-white",children:n.length})]}),v&&e.jsx("div",{className:"top-fill filter-form-holder absolute left-0 z-10 mt-4 w-[500px] min-w-[90%] rounded-md border border-gray-200 bg-white shadow-lg",children:e.jsxs("div",{className:"p-4",children:[n==null?void 0:n.map((t,r)=>e.jsxs("div",{className:"mb-2 flex w-full items-center justify-between gap-3 text-gray-600",children:[e.jsx("div",{className:" mb-3 w-1/3 rounded-md border border-gray-300 px-3 py-2 leading-tight text-gray-700 outline-none",children:t}),e.jsxs("select",{className:"mb-3 w-[40%] rounded-md border px-3 py-2 leading-tight text-gray-700 outline-none",onChange:s=>{G(s.target.value)},children:[e.jsx("option",{value:"eq",selected:!0,children:"equals"}),e.jsx("option",{value:"cs",children:"contains"}),e.jsx("option",{value:"sw",children:"start with"}),e.jsx("option",{value:"ew",children:"ends with"}),e.jsx("option",{value:"lt",children:"lower than"}),e.jsx("option",{value:"le",children:"lower or equal"}),e.jsx("option",{value:"ge",children:"greater or equal"}),e.jsx("option",{value:"gt",children:"greater than"}),e.jsx("option",{value:"bt",children:"between"}),e.jsx("option",{value:"in",children:"in"}),e.jsx("option",{value:"is",children:"is null"})]}),e.jsx("input",{type:"text",placeholder:"Enter value...",className:" mb-3 w-1/3 rounded-md border px-3 py-2 leading-tight text-gray-700 outline-none",onChange:s=>k(t,P,s.target.value)}),e.jsx("div",{className:"mt-[-10px] w-1/12",children:e.jsx(he,{className:" cursor-pointer text-xl",onClick:()=>{p(s=>s.filter(i=>i!==t)),m(s=>s.filter(i=>!i.includes(t)))}})})]},r)),e.jsxs("div",{className:"search-buttons relative flex items-center justify-between font-semibold",children:[e.jsxs("div",{className:"mr-2 flex w-auto cursor-pointer items-center gap-2 rounded bg-gray-100 px-4 py-2.5 font-medium leading-tight text-gray-600 transition duration-150 ease-in-out ",onClick:()=>{S(!N)},children:[e.jsx(ue,{}),"Add filter"]}),N&&e.jsx("div",{className:"absolute top-11 z-10 bg-white px-5 py-3 text-gray-600 shadow-md",children:e.jsx("ul",{className:"flex flex-col gap-2 text-gray-500",children:f.slice(0,-1).map(t=>e.jsx("li",{className:`${n.includes(t.header)?"cursor-not-allowed text-gray-400":"cursor-pointer"}`,onClick:()=>{n.includes(t.header)||p(r=>[...r,t.accessor]),S(!1)},children:t.header},t.header))})}),n.length>0&&e.jsx("div",{onClick:()=>{p([]),m([])},className:"inline-block cursor-pointer  rounded px-6  py-2.5 font-medium leading-tight text-gray-600  transition duration-150 ease-in-out",children:"Clear all filter"})]})]})})]}),e.jsxs("div",{className:" flex cursor-pointer items-center justify-between gap-3 rounded-md border border-gray-200 px-2 py-1 focus-within:border-gray-400",children:[e.jsx(ie,{className:"text-xl text-gray-200"}),e.jsx("input",{type:"text",placeholder:"search...",className:"border-none p-0 placeholder:text-left  focus:outline-none",style:{boxShadow:"0 0 transparent"},onInput:t=>{var r;return k("name","cs",(r=t.target)==null?void 0:r.value)}}),e.jsx(xe,{className:"text-lg text-gray-200"})]})]})})}),M?e.jsx(ge,{}):e.jsx("div",{className:"overflow-x-auto rounded-md border border-gray-200 shadow ",children:e.jsxs("table",{className:"min-w-full divide-y divide-gray-200",children:[e.jsx("thead",{className:"bg-gray-50",children:e.jsx("tr",{children:f.map((t,r)=>e.jsxs("th",{scope:"col",className:"px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500",children:[t.header,e.jsx("span",{children:t.isSorted?t.isSortedDesc?" ▼":" ▲":""})]},r))})}),e.jsx("tbody",{className:"divide-y divide-gray-200 bg-white",children:$.map((t,r)=>e.jsx("tr",{children:f.map((s,i)=>s.accessor==""?e.jsx("td",{className:"whitespace-nowrap px-6 py-4"},i):s.mapping?e.jsx("td",{className:"whitespace-nowrap px-6 py-4",children:s.mapping[t[s.accessor]]},i):s.type==="timestamp"?e.jsx("td",{className:"whitespace-nowrap px-6 py-4",children:new Date(t[s.accessor]*1e3).toLocaleString("en-US")},i):s.type==="currency"?e.jsxs("td",{className:"whitespace-nowrap px-6 py-4",children:["$",Number(t[s.accessor]/100).toFixed(2)]},i):s.type==="metadata"?e.jsx("td",{className:"whitespace-nowrap px-6 py-4",children:t[s.pre_accessor][s.accessor]??"n/a"},i):e.jsx("td",{className:"whitespace-nowrap px-6 py-4",children:t[s.accessor]},i))},r))})]})}),e.jsx(me,{currentPage:c,pageCount:w,pageSize:o,canPreviousPage:T,canNextPage:I,updatePageSize:U,previousPage:Y,nextPage:J})]})},ot=ee(fe,"invoicing","You don't have permission to access invoicing management");export{ot as default};
