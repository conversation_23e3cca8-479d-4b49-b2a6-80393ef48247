import{j as e}from"./@nivo/heatmap-ba1ecfff.js";import{M as J}from"./MembershipCard-72985b8a.js";import{r as i,f as $,b as q}from"./vendor-851db8c1.js";import{n as O,o as H,G as K,m as Q,v as f,d as X,M as Z,b as h,t as v}from"./index-12adfaa3.js";import{B as P}from"./BackButton-11ba52b2.js";import{b as ee,c as se}from"./index.esm-c561e951.js";import{S as re}from"./index.esm-92169588.js";import"./@craftjs/core-d3c11b68.js";import"./@fortawesome/react-fontawesome-13437837.js";import"./@fortawesome/fontawesome-svg-core-4fa3e289.js";import"./react-confirm-alert-cd7ccfe7.js";import"./@tanstack/react-query-20158223.js";import"./@stripe/stripe-js-6b714a86.js";import"./moment-a9aaa855.js";import"./cal-heatmap-cf010ec4.js";import"./react-icons-51bc3cff.js";import"./smoothscroll-polyfill-a5c0a116.js";import"./date-fns-07266b7d.js";import"./lodash-91d5d207.js";import"./numeral-ea653b2a.js";import"./@stripe/react-stripe-js-64f0e61f.js";import"./react-hook-form-687afde5.js";import"./react-select-c8303602.js";import"./@mantine/core-8cbffb6d.js";import"./@emotion/react-89b506c3.js";import"./@emotion/cache-9a5b99cd.js";import"./@emotion/utils-8a8f62c5.js";import"./@emotion/serialize-460cad7f.js";import"./@uppy/dashboard-4a19149e.js";import"./@fullcalendar/core-8ccc1ac4.js";import"./@uppy/core-0760343f.js";import"./@uppy/aws-s3-c5961f7a.js";import"./@uppy/compressor-11f993e4.js";import"./@headlessui/react-a5400090.js";import"./@fortawesome/free-solid-svg-icons-0a9c4907.js";import"./@fortawesome/free-regular-svg-icons-0a88e957.js";import"./@fortawesome/free-brands-svg-icons-fae0dcac.js";const te=p=>{const c={size:24,className:"text-white"},d={Visa:{icon:e.jsx(ee,{...c}),bgColor:"bg-[#1A1F71]"},Mastercard:{icon:e.jsx(se,{...c}),bgColor:"bg-[#EB001B]"},"American Express":{icon:e.jsx(re,{...c}),bgColor:"bg-[#006FCF]"},Discover:{text:"DISC",bgColor:"bg-[#FF6000]"},"Diners Club":{icon:e.jsx(O,{...c}),bgColor:"bg-[#0069AA]"},JCB:{icon:e.jsx(H,{...c}),bgColor:"bg-[#0B4EA2]"},UnionPay:{text:"UP",bgColor:"bg-[#00447C]"}}[p];return d?e.jsx("div",{className:`flex h-8 w-12 items-center justify-center rounded ${d.bgColor} text-white`,children:d.icon||e.jsx("span",{className:"text-sm font-bold",children:d.text})}):null};let l=new Z;function We(){var k,M;const[p,c]=i.useState([]),[N,d]=i.useState(!0),[A,w]=i.useState(!1),[F,ae]=i.useState(null),[s,E]=i.useState(null),[y,S]=i.useState(1),[a,I]=i.useState(null),[T,C]=i.useState(!1),u=$(),{dispatch:n}=q.useContext(K),b=localStorage.getItem("user"),[g,_]=i.useState(null),[m,D]=i.useState(null),U=async()=>{var t,r,j;d(!0);try{l.setTable("user");const x=await l.callRestAPI({id:b},"GET");l.setTable("clubs");const o=await l.callRestAPI({id:(t=x==null?void 0:x.model)==null?void 0:t.club_id},"GET");_(o.model),console.log("view model response",(r=o==null?void 0:o.model)==null?void 0:r.membership_settings),c(JSON.parse((j=o==null?void 0:o.model)==null?void 0:j.membership_settings)||[])}catch(x){console.log(x)}finally{d(!1)}};async function R(){var t;try{w(!0);const{data:r,error:j,message:x}=await l.getCustomerStripeCards();if(console.log(r),j&&h(n,x,5e3),!r)return;const o=(t=r==null?void 0:r.data)==null?void 0:t.find(Y=>{var L,B;return Y.id===((B=(L=r==null?void 0:r.data[0])==null?void 0:L.customer)==null?void 0:B.default_source)});I(o)}catch(r){console.error("ERROR",r),h(n,r.message,5e3),v(dispatch,r.code)}finally{w(!1)}}i.useEffect(()=>{U(),R(),Q({title:"Membership",path:"/user/membership",clubName:g==null?void 0:g.name,favicon:g==null?void 0:g.club_logo,description:"Membership"})},[]);const z=t=>{E(t),S(2)},G=async()=>{const t=s;C(!0);try{if(m!=null&&m.subId){const r=await l.changeStripeSubscription({userId:b,activeSubscriptionId:m.subId,newPlanId:t.plan_id});r.error?(console.error(r.message),h(n,r.message,7500,"error")):(h(n,"Subscription updated successfully",3e3),u("/user/profile?tab=membership"))}else{const r=await l.createStripeSubscription({planId:t.plan_id});r.error?(console.error(r.message),h(n,r.message,7500,"error")):(h(n,"Subscription created successfully",3e3),u("/user/profile?tab=membership"))}}catch(r){console.error("Error",r),h(n,r.message,7500,"error"),v(n,r.code)}finally{C(!1)}};async function V(){try{const t=await l.getCustomerStripeSubscription(b);D(t.customer)}catch(t){console.error(t),v(dispatch,t.code)}}i.useEffect(()=>{V()},[b]);const W=()=>e.jsxs("div",{className:"h-fit animate-pulse rounded-xl border border-gray-200 bg-white p-5 shadow-sm",children:[e.jsxs("div",{className:"mb-3 flex items-center justify-between border-b border-gray-100 pb-4",children:[e.jsx("div",{className:"h-6 w-32 rounded bg-gray-200"}),e.jsx("div",{className:"h-6 w-16 rounded bg-gray-200"})]}),e.jsxs("div",{className:"mb-4",children:[e.jsx("div",{className:"mb-2 h-8 w-24 rounded bg-gray-200"}),e.jsx("div",{className:"h-4 w-16 rounded bg-gray-200"})]}),e.jsxs("div",{className:"mb-4 rounded-lg bg-gray-50 p-3",children:[e.jsx("div",{className:"mb-2 h-5 w-40 rounded bg-gray-200"}),e.jsx("div",{className:"grid grid-cols-2 gap-2",children:[1,2,3,4].map(t=>e.jsx("div",{className:"h-4 rounded bg-gray-200"},t))})]}),e.jsx("div",{className:"mb-4 space-y-3",children:[1,2,3].map(t=>e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx("div",{className:"h-5 w-5 rounded bg-gray-200"}),e.jsx("div",{className:"h-4 flex-1 rounded bg-gray-200"})]},t))}),e.jsx("div",{className:"h-10 w-full rounded-lg bg-gray-200"})]});return e.jsx("div",{className:"min-h-screen bg-gradient-to-br from-gray-50 to-gray-100",children:e.jsxs("div",{className:"mx-auto max-w-7xl px-4 py-6 sm:px-6 lg:px-8",children:[e.jsx("div",{className:"mb-6",children:e.jsx(P,{onBack:()=>{y===1?u(-1):S(1)}})}),y===1&&e.jsxs("div",{className:"space-y-8",children:[e.jsxs("div",{className:"text-center",children:[e.jsx("h1",{className:"text-3xl font-bold text-gray-900 sm:text-4xl lg:text-5xl",children:"Choose Your Membership"}),e.jsx("p",{className:"mx-auto mt-4 max-w-2xl text-lg text-gray-600",children:"Select the perfect plan that fits your needs and start enjoying all the benefits"})]}),e.jsx("div",{className:"grid grid-cols-1 gap-6 sm:grid-cols-2 lg:grid-cols-3 xl:gap-8",children:N?Array.from({length:3}).map((t,r)=>e.jsx(W,{},r)):(p==null?void 0:p.length)>0?p.map(t=>e.jsx("div",{className:"transform transition-all duration-200 hover:scale-105",children:e.jsx(J,{...t,isCurrentPlan:F===t.id,onSelect:()=>z(t),isActive:(m==null?void 0:m.planId)===t.plan_id})},t.plan_name)):e.jsxs("div",{className:"col-span-full py-12 text-center",children:[e.jsx("div",{className:"mx-auto h-24 w-24 text-gray-400",children:e.jsx("svg",{fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:e.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:1,d:"M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"})})}),e.jsx("h3",{className:"mt-4 text-lg font-medium text-gray-900",children:"No membership plans available"}),e.jsx("p",{className:"mt-2 text-gray-500",children:"Please contact support for assistance."})]})})]}),y===2&&e.jsxs("div",{className:"space-y-8",children:[e.jsxs("div",{className:"text-center",children:[e.jsx("h1",{className:"text-3xl font-bold text-gray-900 sm:text-4xl",children:(s==null?void 0:s.price)===0?"Confirm Your Selection":"Complete Your Purchase"}),e.jsx("p",{className:"mt-2 text-lg text-gray-600",children:(s==null?void 0:s.price)===0?"Review your free plan selection and activate your membership":"Review your selection and payment details"})]}),e.jsx("div",{className:"mx-auto max-w-6xl",children:e.jsxs("div",{className:"grid grid-cols-1 gap-8 lg:grid-cols-2",children:[e.jsx("div",{className:"order-2 lg:order-1",children:e.jsxs("div",{className:"overflow-hidden rounded-2xl border border-gray-100 bg-white shadow-xl",children:[e.jsx("div",{className:"bg-gradient-to-r from-blue-600 to-indigo-600 px-6 py-4",children:e.jsx("h2",{className:"text-lg font-semibold text-white",children:"Membership Details"})}),e.jsxs("div",{className:"p-6",children:[e.jsxs("div",{className:"mb-6",children:[e.jsx("h3",{className:"mb-2 text-2xl font-bold text-gray-900",children:s==null?void 0:s.plan_name}),e.jsx("div",{className:"text-3xl font-bold text-blue-600",children:(s==null?void 0:s.price)===0?e.jsx("span",{className:"text-green-600",children:"Free"}):e.jsxs(e.Fragment,{children:[f(s==null?void 0:s.price),e.jsx("span",{className:"ml-1 text-lg font-normal text-gray-500",children:"/month"})]})})]}),e.jsxs("div",{className:"border-t border-gray-200 pt-6",children:[e.jsx("h4",{className:"mb-4 font-semibold text-gray-900",children:"Features included:"}),e.jsx("div",{className:"space-y-3",children:(k=s==null?void 0:s.features)==null?void 0:k.map((t,r)=>e.jsxs("div",{className:"flex items-center gap-3",children:[e.jsx("div",{className:"flex h-6 w-6 flex-shrink-0 items-center justify-center rounded-full bg-green-100",children:e.jsx("svg",{className:"h-4 w-4 text-green-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:e.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M5 13l4 4L19 7"})})}),e.jsx("span",{className:"text-gray-700",children:t.text})]},r))})]})]})]})}),e.jsx("div",{className:"order-1 lg:order-2",children:e.jsxs("div",{className:"overflow-hidden rounded-2xl border border-gray-100 bg-white shadow-xl",children:[e.jsx("div",{className:`px-6 py-4 ${(s==null?void 0:s.price)===0,"bg-gradient-to-r from-green-600 to-emerald-600"}`,children:e.jsx("h2",{className:"text-lg font-semibold text-white",children:(s==null?void 0:s.price)===0?"Subscription Details":"Payment Details"})}),e.jsxs("div",{className:"p-6",children:[e.jsx("div",{className:"mb-8",children:e.jsxs("div",{className:"space-y-4",children:[e.jsx("div",{className:"flex justify-between text-sm uppercase tracking-wide text-gray-600",children:e.jsx("span",{children:(s==null?void 0:s.price)===0?"Plan Summary":"Billing Summary"})}),e.jsxs("div",{className:"space-y-3",children:[e.jsxs("div",{className:"flex justify-between",children:[e.jsx("span",{className:"text-gray-700",children:"Plan price"}),e.jsx("span",{className:"font-medium",children:(s==null?void 0:s.price)===0?"Free":f(s==null?void 0:s.price)})]}),(s==null?void 0:s.price)>0&&e.jsxs("div",{className:"flex justify-between",children:[e.jsx("span",{className:"text-gray-700",children:"Taxes & fees"}),e.jsx("span",{className:"font-medium",children:f(.97)})]})]}),e.jsx("div",{className:"border-t border-gray-200 pt-4",children:e.jsxs("div",{className:"flex justify-between text-lg font-bold",children:[e.jsx("span",{children:"Total"}),e.jsx("span",{className:"text-blue-600",children:(s==null?void 0:s.price)===0?"Free":f((s==null?void 0:s.price)+.97)})]})})]})}),(s==null?void 0:s.price)>0?e.jsxs("div",{className:"mb-6",children:[e.jsx("h4",{className:"mb-4 font-semibold text-gray-900",children:"Payment Method"}),A?e.jsx("div",{className:"animate-pulse",children:e.jsx("div",{className:"h-16 rounded-lg bg-gray-200"})}):a?e.jsxs("div",{className:"flex items-center justify-between rounded-xl border-2 border-gray-200 bg-gray-50 p-4",children:[e.jsxs("div",{className:"flex items-center space-x-3",children:[te(a==null?void 0:a.brand),e.jsxs("div",{children:[e.jsxs("p",{className:"font-medium text-gray-900",children:[(M=a==null?void 0:a.brand)==null?void 0:M.toUpperCase()," ••••"," ",a==null?void 0:a.last4]}),e.jsx("p",{className:"text-sm text-gray-500",children:"Default payment method"})]})]}),e.jsx("button",{onClick:()=>u("/user/profile?tab=payment-methods"),className:"text-sm font-medium text-blue-600 transition-colors hover:text-blue-700",children:"Change"})]}):e.jsx("div",{className:"rounded-xl border-2 border-red-200 bg-red-50 p-4",children:e.jsxs("div",{className:"flex items-start space-x-3",children:[e.jsx("div",{className:"flex-shrink-0",children:e.jsx("svg",{className:"h-5 w-5 text-red-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:e.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"})})}),e.jsxs("div",{children:[e.jsx("p",{className:"mb-2 text-sm font-medium text-red-800",children:"Payment method required"}),e.jsx("p",{className:"mb-3 text-sm text-red-700",children:"Please add a payment method to continue with your subscription."}),e.jsx("button",{onClick:()=>u("/user/profile?tab=payment-methods"),className:"text-sm font-medium text-red-600 underline transition-colors hover:text-red-700",children:"Add Payment Method →"})]})]})})]}):e.jsx("div",{className:"mb-6",children:e.jsx("div",{className:"rounded-xl border-2 border-green-200 bg-green-50 p-4",children:e.jsxs("div",{className:"flex items-start space-x-3",children:[e.jsx("div",{className:"flex-shrink-0",children:e.jsx("svg",{className:"h-5 w-5 text-green-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:e.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"})})}),e.jsxs("div",{children:[e.jsx("p",{className:"mb-2 text-sm font-medium text-green-800",children:"No payment required"}),e.jsx("p",{className:"text-sm text-green-700",children:"This is a free plan! Click the button below to activate your membership instantly."})]})]})})}),e.jsx(X,{onClick:G,loading:T,disabled:(s==null?void 0:s.price)>0&&!a,className:`w-full rounded-xl py-4 text-lg font-semibold transition-all duration-200 ${(s==null?void 0:s.price)===0||a?"transform bg-gradient-to-r from-blue-600 to-indigo-600 text-white shadow-lg hover:scale-[1.02] hover:from-blue-700 hover:to-indigo-700 hover:shadow-xl":"cursor-not-allowed bg-gray-300 text-gray-500"}`,children:(s==null?void 0:s.price)===0?"Activate Free Plan":a?"Complete Purchase":"Add payment method to continue"}),e.jsx("p",{className:"mt-6 text-center text-xs leading-relaxed text-gray-500",children:(s==null?void 0:s.price)===0?"By activating this free plan, you agree to our Terms of Service and Privacy Policy.":"By completing this purchase, you agree to our Terms of Service and Privacy Policy. Your subscription will automatically renew monthly unless cancelled."})]})]})})]})})]})]})})}export{We as default};
