import{j as t}from"./@nivo/heatmap-ba1ecfff.js";import{r as g,b as M}from"./vendor-851db8c1.js";import{M as j,G as P,R as N,H as R,E as k,J as F,b as _}from"./index-12adfaa3.js";let o=new j;const w=()=>[{id:"dashboard",name:"Dashboard",category:"Core"},{id:"customer_support",name:"Customer Support",category:"Support"},{id:"daily_scheduler",name:"Daily Scheduler",category:"Scheduling"},{id:"court_management",name:"Court Management",category:"Management"},{id:"availability_dashboard",name:"Availability Dashboard",category:"Scheduling"},{id:"availability_dashboard_all",name:"Availability Dashboard (All Staff)",category:"Scheduling"},{id:"club_ui",name:"Club UI",category:"Management"},{id:"court_reservation",name:"Court Reservation",category:"Booking"},{id:"lessons",name:"Lessons",category:"Programs"},{id:"clinics",name:"Clinics",category:"Programs"},{id:"custom_requests",name:"Custom Requests",category:"Booking"},{id:"find_a_buddy",name:"Find-A-Buddy",category:"Social"},{id:"general_faqs",name:"General FAQs",category:"Support"},{id:"coach",name:"Coach",category:"Staff Management"},{id:"user",name:"User",category:"User Management"},{id:"staff",name:"Staff",category:"Staff Management"},{id:"fee_settings",name:"Fee Settings",category:"Financial"},{id:"email",name:"Email",category:"Communication"},{id:"invoicing",name:"Invoicing",category:"Financial"},{id:"invoicing_bank",name:"Invoicing - Club Bank Details",category:"Financial"},{id:"history",name:"History",category:"Audit"}],B=()=>[{id:"dashboard",name:"Dashboard",category:"Core"},{id:"customer_support",name:"Customer Support",category:"Support"},{id:"daily_scheduler",name:"Daily Scheduler",category:"Scheduling"},{id:"court_management",name:"Court Management",category:"Management"},{id:"availability_dashboard",name:"Availability Dashboard",category:"Scheduling"},{id:"availability_dashboard_all",name:"Availability Dashboard (All Staff)",category:"Scheduling"},{id:"club_ui",name:"Club UI",category:"Management"},{id:"court_reservation",name:"Court Reservation",category:"Booking"},{id:"lessons",name:"Lessons",category:"Programs"},{id:"clinics",name:"Clinics",category:"Programs"},{id:"custom_requests",name:"Custom Requests",category:"Booking"},{id:"find_a_buddy",name:"Find-A-Buddy",category:"Social"},{id:"coach",name:"Coach",category:"Staff Management"},{id:"user",name:"User",category:"User Management"},{id:"staff",name:"Staff",category:"Staff Management"},{id:"fee_settings",name:"Fee Settings",category:"Financial"},{id:"email",name:"Email",category:"Communication"},{id:"invoicing",name:"Invoicing",category:"Financial"},{id:"invoicing_bank",name:"Invoicing - Club Bank Details",category:"Financial"},{id:"history",name:"History",category:"Audit"}],E=d=>d==="admin"?w():B(),c={staff:{dashboard:!0,customer_support:!0,daily_scheduler:!0,court_management:!0,availability_dashboard:!0,availability_dashboard_all:!0,club_ui:!0,lessons:!0,clinics:!0,custom_requests:!0,find_a_buddy:!0,coach:!0,staff:!1,user:!0,fee_settings:!0,email:!0,invoicing:!0,invoicing_bank:!0,history:!0,court_reservation:!0,general_faqs:!0}};function q({isOpen:d,onClose:u,roleAccessData:s,club:l}){const[v,f]=g.useState(!1),{dispatch:y}=M.useContext(P),[m,r]=g.useState(c),n=localStorage.getItem("role"),h=E(n),x=n==="admin"?"Admin Staff":"Staff";console.log("userRole",n),console.log("modules",h.map(a=>a.id)),g.useEffect(()=>{if(s!=null&&s.permission)try{const a=JSON.parse(s.permission);console.log("parsedPermissions",a);const e={staff:{...c.staff,...a.staff}};console.log("finalPermissions",e),r(e)}catch(a){console.error("Error parsing permissions:",a),r(c)}else r(c)},[s]);const S=a=>{n==="staff"&&a==="staff"||r(e=>{var i;return{...e,staff:{...e.staff,[a]:!((i=e.staff)!=null&&i[a])}}})},C=async()=>{f(!0);try{const a=n==="admin"?0:(l==null?void 0:l.id)||0,e={club_id:a,permission:JSON.stringify(m)};o.setTable("club_permissions"),s!=null&&s.id?(e.id=s.id,await o.callRestAPI(e,"PUT")):await o.callRestAPI(e,"POST"),await R(o,{user_id:localStorage.getItem("user"),activity_type:k.club_ui,action_type:F.UPDATE,data:e,club_id:a,description:`Updated ${n==="admin"?"admin staff":"staff"} role access`}),_(y,"Permissions saved successfully",3e3,"success"),u()}catch(a){console.log(a),_(y,"Error saving permissions",3e3,"error")}finally{f(!1)}};return t.jsx(N,{isOpen:d,onClose:u,title:"Role access",onPrimaryAction:C,primaryButtonText:"Save changes",submitting:v,children:t.jsxs("div",{className:"flex flex-col",children:[t.jsxs("div",{className:"mb-4 grid grid-cols-[1fr,auto] items-center gap-4 px-2",children:[t.jsx("div",{className:"text-sm font-medium text-gray-500",children:"Module"}),t.jsx("div",{className:"text-sm font-medium text-gray-500",children:x})]}),t.jsx("div",{className:"flex flex-col gap-4",children:Object.entries(h.reduce((a,e)=>{const i=e.category||"Other";return a[i]||(a[i]=[]),a[i].push(e),a},{})).map(([a,e])=>t.jsxs("div",{className:"space-y-2",children:[t.jsx("div",{className:"border-b border-gray-200 pb-1",children:t.jsx("h3",{className:"text-xs font-semibold uppercase tracking-wider text-gray-500",children:a})}),t.jsx("div",{className:"space-y-1",children:e.map(i=>{var b,p;return t.jsxs("div",{className:"grid grid-cols-[1fr,auto] items-center gap-4 rounded-lg px-2 py-2 hover:bg-gray-50",children:[t.jsx("div",{className:"flex flex-col",children:t.jsx("span",{className:"text-sm text-gray-900",children:i.name})}),t.jsx("button",{onClick:()=>!(n==="staff"&&i.id==="staff")&&S(i.id),disabled:n==="staff"&&i.id==="staff",className:`h-6 w-11 rounded-full p-1 transition-colors duration-200 ${(b=m.staff)!=null&&b[i.id]?"bg-blue-600":"bg-gray-200"} ${n==="staff"&&i.id==="staff"?"cursor-not-allowed opacity-50":""}`,children:t.jsx("div",{className:`h-4 w-4 rounded-full bg-white transition-transform duration-200 ${(p=m.staff)!=null&&p[i.id]?"translate-x-5":"translate-x-0"}`})})]},i.id)})})]},a))})]})})}export{q as R};
