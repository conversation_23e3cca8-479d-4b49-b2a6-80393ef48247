import{j as t}from"./@nivo/heatmap-ba1ecfff.js";import{b as i,f as k,j as E,r as P}from"./vendor-851db8c1.js";import{u as A}from"./react-hook-form-687afde5.js";import{o as C}from"./yup-2824f222.js";import{c as $,a as q,b as T}from"./yup-54691517.js";import{M as D,A as F,G,t as h,b}from"./index-12adfaa3.js";import"./@craftjs/core-d3c11b68.js";import"./@fortawesome/react-fontawesome-13437837.js";import"./@fortawesome/fontawesome-svg-core-4fa3e289.js";import"./@hookform/resolvers-67648cca.js";import"./react-confirm-alert-cd7ccfe7.js";import"./@tanstack/react-query-20158223.js";import"./@stripe/stripe-js-6b714a86.js";import"./moment-a9aaa855.js";import"./cal-heatmap-cf010ec4.js";import"./react-icons-51bc3cff.js";import"./smoothscroll-polyfill-a5c0a116.js";import"./date-fns-07266b7d.js";import"./lodash-91d5d207.js";import"./numeral-ea653b2a.js";import"./@stripe/react-stripe-js-64f0e61f.js";import"./react-select-c8303602.js";import"./@mantine/core-8cbffb6d.js";import"./@emotion/react-89b506c3.js";import"./@emotion/cache-9a5b99cd.js";import"./@emotion/utils-8a8f62c5.js";import"./@emotion/serialize-460cad7f.js";import"./@uppy/dashboard-4a19149e.js";import"./@fullcalendar/core-8ccc1ac4.js";import"./@uppy/core-0760343f.js";import"./@uppy/aws-s3-c5961f7a.js";import"./@uppy/compressor-11f993e4.js";import"./@headlessui/react-a5400090.js";import"./@fortawesome/free-solid-svg-icons-0a9c4907.js";import"./@fortawesome/free-regular-svg-icons-0a88e957.js";import"./@fortawesome/free-brands-svg-icons-fae0dcac.js";let f=new D;const ye=()=>{var d,p;const g=$({name:q().required(),status:T().required()}).required(),{dispatch:m}=i.useContext(F),{dispatch:r}=i.useContext(G),y=k(),s=E(),[I,j]=P.useState(0),{register:n,handleSubmit:v,setError:w,setValue:l,formState:{errors:c}}=A({resolver:C(g)}),N=[{key:"0",value:"Inactive"},{key:"1",value:"Active"}],S=async e=>{try{console.log(e);const o=await f.updateStripePrice(s==null?void 0:s.id,e);if(!o.error)b(r,"Edited",4e3),y("/admin/prices");else if(o.validation){const u=Object.keys(o.validation);for(let a=0;a<u.length;a++){const x=u[a];w(x,{type:"manual",message:o.validation[x]})}}}catch(o){console.log("Error",o),b(r,o.message,4e3),h(m,o.message)}};return i.useEffect(()=>{r({type:"SETPATH",payload:{path:"prices"}}),async function(){try{const e=await f.getStripePrice(s==null?void 0:s.id);if(!e.error){const o=e.model.object;l("name",o.nickname),l("status",e.model.status),j(e.model.id)}}catch(e){console.log("Error",e),h(m,e.message)}}()},[]),t.jsxs("div",{className:" shadow-md rounded   mx-auto p-5",children:[t.jsx("h4",{className:"text-2xl font-medium",children:"Edit Product"}),t.jsxs("form",{className:"w-full max-w-lg",onSubmit:v(S),children:[t.jsxs("div",{className:"mb-4",children:[t.jsx("label",{className:"block text-gray-700 text-sm font-bold mb-2",htmlFor:"name",children:"Name"}),t.jsx("input",{type:"text",placeholder:"Name",...n("name"),className:`"shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline ${(d=c.name)!=null&&d.message?"border-red-500":""}`}),t.jsx("p",{className:"text-red-500 text-xs italic",children:(p=c.name)==null?void 0:p.message})]}),t.jsxs("div",{className:"mb-4",children:[t.jsx("label",{className:"block text-gray-700 text-sm font-bold mb-2",children:"Status"}),t.jsx("select",{className:"shadow appearance-none border  rounded w-full py-2 px-3 text-gray-700 mb-3 leading-tight focus:outline-none focus:shadow-outline",...n("status"),children:N.map(e=>t.jsx("option",{value:e.key,children:e.value},e.key))})]}),t.jsx("button",{type:"submit",className:"bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded focus:outline-none focus:shadow-outline",children:"Submit"})]})]})};export{ye as default};
