import{j as e}from"./@nivo/heatmap-ba1ecfff.js";import{r as m,b as i,f as we}from"./vendor-851db8c1.js";import{an as Ee,d as ye,ak as De,M as fe,G as be,A as ve,R as Te,v as ge,a3 as $,e as je,ab as ke,b as W,T as Fe,c as Ae,E as Pe,K as Le,t as he,X as xe}from"./index-12adfaa3.js";import{c as Be,a as X}from"./yup-54691517.js";import{u as Ne}from"./react-hook-form-687afde5.js";import{o as Oe}from"./yup-2824f222.js";import{P as Me}from"./index-eb1bc208.js";import"./lodash-91d5d207.js";import Re from"./Skeleton-1e8bf077.js";import{S as I}from"./react-select-c8303602.js";import{H as Ie}from"./HistoryComponent-90e771dd.js";import{D as $e}from"./DataTable-a2248415.js";const ze=({onClose:o,onConfirm:x,eventCounts:b})=>{const[h,g]=m.useState(""),a=()=>{h&&x(h)};return e.jsxs("div",{className:"fixed inset-0 z-[99999] flex items-center justify-center ",children:[e.jsx("div",{className:"fixed inset-0 bg-black opacity-50"}),e.jsxs("div",{className:"relative z-50 w-full max-w-2xl rounded-lg bg-white p-6",children:[e.jsxs("div",{className:"flex items-center justify-between border-b pb-4",children:[e.jsx("h2",{className:"text-2xl font-bold",children:"Confirm Updates"}),e.jsx("button",{onClick:o,className:"text-gray-500 hover:text-gray-700",children:e.jsx(Ee,{className:"h-6 w-6"})})]}),e.jsxs("div",{className:"py-4",children:[e.jsx("p",{className:"mb-4 text-base",children:"You're changing the Sport / Type / Subtype for this clinic. This affects how the clinic connects to scheduled events; time slots, availability, and visibility in the scheduler."}),e.jsx("p",{className:"mb-2 font-medium",children:"Below are the scheduled events currently tied to this clinic:"}),e.jsxs("div",{className:"mb-6 ml-6",children:[e.jsxs("p",{children:["Total Scheduled Events: ",b.total]}),e.jsxs("p",{children:["Completed Events: ",b.completed]}),e.jsxs("p",{children:["Upcoming Events: ",b.upcoming]}),e.jsxs("p",{children:["Last Event Date: ",b.lastEvent]})]}),e.jsx("p",{className:"mb-4 font-medium",children:"Choose how to handle scheduled events:"}),e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{className:"flex items-start",children:[e.jsx("input",{type:"radio",id:"option1",name:"sportChangeOption",value:"1",checked:h==="1",onChange:r=>g(r.target.value),className:"mt-1 h-4 w-4"}),e.jsxs("div",{className:"ml-2",children:[e.jsx("label",{htmlFor:"option1",className:"font-medium",children:"Cancel All Future Events"}),e.jsx("p",{className:"text-sm text-gray-600",children:"Delete all upcoming events for this clinic. Past events will remain unchanged."})]})]}),e.jsxs("div",{className:"flex items-start",children:[e.jsx("input",{type:"radio",id:"option2",name:"sportChangeOption",value:"2",checked:h==="2",onChange:r=>g(r.target.value),className:"mt-1 h-4 w-4"}),e.jsxs("div",{className:"ml-2",children:[e.jsx("label",{htmlFor:"option2",className:"font-medium",children:"Apply Changes Only to Future Events"}),e.jsx("p",{className:"text-sm text-gray-600",children:"Keep past events intact: Update sport/type/subtype on upcoming events only."})]})]}),e.jsxs("div",{className:"flex items-start",children:[e.jsx("input",{type:"radio",id:"option3",name:"sportChangeOption",value:"3",checked:h==="3",onChange:r=>g(r.target.value),className:"mt-1 h-4 w-4"}),e.jsxs("div",{className:"ml-2",children:[e.jsx("label",{htmlFor:"option3",className:"font-medium",children:"Apply Changes to All Events (Past and Future)"}),e.jsx("p",{className:"text-sm text-gray-600",children:"Retroactively apply sport/type/subtype changes to all events connected to this clinic, including completed ones."})]})]}),e.jsxs("div",{className:"flex items-start",children:[e.jsx("input",{type:"radio",id:"option4",name:"sportChangeOption",value:"4",checked:h==="4",onChange:r=>g(r.target.value),className:"mt-1 h-4 w-4"}),e.jsxs("div",{className:"ml-2",children:[e.jsx("label",{htmlFor:"option4",className:"font-medium",children:"Clone This Clinic and Keep Existing Schedule"}),e.jsx("p",{className:"text-sm text-gray-600",children:"Keep the current events tied to this clinic, and create a new clinic with your changes that you can schedule separately."})]})]}),e.jsxs("div",{className:"flex items-start",children:[e.jsx("input",{type:"radio",id:"option0",name:"sportChangeOption",value:"0",checked:h==="0",onChange:r=>g(r.target.value),className:"mt-1 h-4 w-4"}),e.jsxs("div",{className:"ml-2",children:[e.jsx("label",{htmlFor:"option0",className:"font-medium",children:"Don't Apply Sport/Type/Subtype Changes"}),e.jsx("p",{className:"text-sm text-gray-600",children:"Save all other updates to this clinic, but leave the original sport, type, and subtype unchanged."})]})]})]})]}),e.jsxs("div",{className:"flex justify-end gap-3 border-t pt-4",children:[e.jsx("button",{onClick:o,className:"rounded-lg border border-gray-300 bg-white px-6 py-2 text-gray-700 hover:bg-gray-50",children:"Cancel"}),e.jsx(ye,{onClick:a,className:"rounded-lg bg-blue-500 px-6 py-2 text-white hover:bg-blue-600",disabled:!h,children:"Confirm and Save Changes"})]})]})]})},w=De();let Q=new fe;const He=({clinic:o,onClose:x,getData:b,isOpen:h,sports:g})=>{var q,G,R;const a=o||{sport_id:"",type:"",sub_type:"",date:"",end_date:"",start_time:"",end_time:"",name:"",cost_per_head:"",description:"",recurring:0,id:null},{setValue:r,watch:n}=Ne({defaultValues:{sport_id:a.sport_id,type:a.type,sub_type:a.sub_type,date:a.date,end_date:a.end_date,start_time:a.start_time,end_time:a.end_time,name:a.name,cost_per_head:a.cost_per_head,description:a.description,recurring:a.recurring}}),[D,E]=m.useState(!1),[u,z]=m.useState(!1),[Z,v]=m.useState(!1),[T,H]=m.useState([]),[ee,k]=m.useState(!1),[y,te]=m.useState({sport_id:"",type:"",sub_type:""}),[F,j]=m.useState(null),[A,se]=m.useState({total:0,completed:0,upcoming:"No upcoming events",lastEvent:"No events scheduled"}),[ae,P]=m.useState(null),[L,B]=m.useState([]),[O,V]=m.useState([]),[re,M]=m.useState(w),f=n("sport_id"),N=n("type"),_=n("start_time"),{dispatch:S}=m.useContext(be),{dispatch:K}=m.useContext(ve);m.useEffect(()=>{o&&(r("sport_id",o.sport_id||""),r("type",o.type||""),r("sub_type",o.sub_type||""),r("date",o.date||""),r("end_date",o.end_date||""),r("start_time",o.start_time||""),r("end_time",o.end_time||""),r("name",o.name||""),r("cost_per_head",o.cost_per_head||""),r("description",o.description||""),r("recurring",o.recurring||0),te({sport_id:o.sport_id||"",type:o.type||"",sub_type:o.sub_type||""}))},[o,r]);const U=async()=>{if(!a.id)return[];v(!0);try{Q.setTable("clinic_coaches");const s=await Q.callRestAPI({filter:[`clinic_id,eq,${a.id}`]},"GETALL"),l=await ke(S,K,"coach",s==null?void 0:s.list.map(d=>d.coach_id),"user|user_id");H(l.list)}catch(s){return console.log(s),[]}finally{v(!1)}};m.useEffect(()=>{o!=null&&o.id&&U()},[o==null?void 0:o.id]),m.useEffect(()=>{var s;if(f){const l=g.find(d=>d.id.toString()===f.toString());if(l){const d=((s=l.sport_types)==null?void 0:s.filter(t=>t.type!==""))||[];B(d)}else B([])}else B([])},[f,g]),m.useEffect(()=>{if(N){const s=L.find(l=>l.type===N);if(s){const l=(s.subtype||[]).filter(d=>d!=="");V(l)}else V([])}else V([])},[N,L]);const pe=s=>{if(!s)return w;const l=w.findIndex(d=>d.value===s);return l===-1?w:w.filter((d,t)=>t>l)};m.useEffect(()=>{if(_){const s=pe(_);M(s)}else M(w)},[_]);const ne=async()=>{const s=await U();s&&H(s)},le=async s=>{try{return console.log(`Fetching event counts for clinic ID: ${s}`),{total:12,completed:4,upcoming:"April 3, 2025",lastEvent:"June 19, 2025"}}catch(l){return console.log("Error fetching event counts:",l),{total:0,completed:0,upcoming:"No upcoming events",lastEvent:"No events scheduled"}}},oe=async()=>{if(!a.id){W(S,"Cannot save: no clinic selected",3e3,"error");return}const s={id:a.id,name:n("name"),cost_per_head:parseFloat(n("cost_per_head")),description:n("description"),sport_id:n("sport_id"),type:n("type"),sub_type:n("sub_type"),date:n("date"),end_date:n("end_date"),start_time:n("start_time"),end_time:n("end_time"),recurring:n("recurring")===1||n("recurring")===!0?1:0};if(s.sport_id!==y.sport_id||s.type!==y.type||s.sub_type!==y.sub_type){E(!0);try{const d=await le(a.id);se(d),k(!0),P(s)}catch(d){W(S,"Error fetching event information",3e3,"error"),console.log(d)}finally{E(!1)}return}await Y(s)},ie={0:"no_changes",1:"cancel_future",2:"update_future_only",3:"update_all_events",4:"clone_clinic"},Y=async s=>{E(!0);try{const l={...s};F!==null&&(l.sport_change_option=F,l.sport_change_action=ie[F]),console.log("API Payload being sent:",l),Q.setTable("clinics");const d=await Q.callRestAPI(l,"PUT");d!=null&&d.error||(W(S,"Clinic updated successfully",3e3,"success"),o&&Object.keys(l).forEach(t=>{t!=="id"&&(o[t]=l[t])}),te({sport_id:s.sport_id,type:s.type,sub_type:s.sub_type}),z(!1),j(null),await ne(),b())}catch(l){W(S,(l==null?void 0:l.message)||"An error occurred",3e3,"error"),console.log(l)}finally{E(!1)}};if(m.useEffect(()=>{u&&(r("name",a.name||""),r("cost_per_head",a.cost_per_head||""),r("description",a.description||""),r("sport_id",a.sport_id||""),r("type",a.type||""),r("sub_type",a.sub_type||""),r("date",a.date||""),r("end_date",a.end_date||""),r("start_time",a.start_time||""),r("end_time",a.end_time||""),r("recurring",a.recurring))},[u,a,r]),!h)return null;const c={control:s=>({...s,borderRadius:"0.5rem",border:"none",backgroundColor:"#f9fafb","&:hover":{border:"none",backgroundColor:"#f3f4f6"},"&:focus-within":{border:"none",boxShadow:"none",backgroundColor:"#f3f4f6"}}),option:(s,l)=>({...s,backgroundColor:l.isSelected?"#3b82f6":l.isFocused?"#f3f4f6":"white",color:l.isSelected?"white":"#374151","&:hover":{backgroundColor:l.isSelected?"#3b82f6":"#f3f4f6"}}),menu:s=>({...s,borderRadius:"0.5rem",boxShadow:"0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1)"}),multiValue:s=>({...s,backgroundColor:"#e5e7eb",borderRadius:"0.375rem"}),multiValueLabel:s=>({...s,color:"#374151",padding:"0.25rem 0.5rem"}),multiValueRemove:s=>({...s,color:"#6b7280",borderRadius:"0 0.375rem 0.375rem 0","&:hover":{backgroundColor:"#d1d5db",color:"#374151"}})};return e.jsxs(e.Fragment,{children:[e.jsxs(Te,{isOpen:h,onClose:x,title:a.name||"Clinic details",showFooter:!1,children:[e.jsxs("div",{className:"space-y-6",children:[e.jsx("div",{className:"rounded-lg border border-blue-100 bg-gradient-to-r from-blue-50 to-indigo-50 p-4",children:e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{children:[e.jsx("h3",{className:"text-lg font-semibold text-gray-900",children:a.name||"Clinic Details"}),e.jsxs("p",{className:"text-sm text-gray-600",children:[((q=g.find(s=>s.id.toString()==a.sport_id))==null?void 0:q.name)||"No sport selected",a.type&&` • ${a.type}`,a.sub_type&&` • ${a.sub_type}`]})]}),e.jsxs("div",{className:"text-right",children:[e.jsx("div",{className:"text-lg font-bold text-primaryBlue",children:ge(a.cost_per_head)}),e.jsx("div",{className:"text-sm text-gray-500",children:"per person"})]})]})}),e.jsx("div",{className:"flex justify-end border-b border-gray-200 pb-4",children:u?e.jsxs("div",{className:"flex gap-3",children:[e.jsx("button",{onClick:()=>z(!1),className:"rounded-lg border border-gray-300 bg-white px-4 py-2 font-medium text-gray-700 transition-colors hover:border-gray-400 hover:bg-gray-50",children:"Cancel"}),e.jsx(ye,{loading:D,onClick:oe,className:"rounded-lg bg-primaryBlue px-6 py-2 font-medium text-white shadow-sm transition-all hover:bg-primaryBlue/90 hover:shadow-md",children:D?"Saving...":"Save All Changes"})]}):e.jsx("button",{onClick:()=>z(!0),className:"rounded-lg bg-primaryBlue px-6 py-2 font-medium text-white shadow-sm transition-all hover:bg-primaryBlue/90 hover:shadow-md",children:e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsx("svg",{className:"h-4 w-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:e.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"})}),e.jsx("span",{children:"Edit Details"})]})})}),e.jsxs("div",{className:"space-y-6",children:[e.jsx("div",{className:"border-b border-gray-200 pb-2",children:e.jsx("h4",{className:"text-sm font-semibold uppercase tracking-wide text-gray-900",children:"Basic Information"})}),e.jsxs("div",{className:"space-y-2",children:[e.jsx("label",{className:"text-sm font-medium text-gray-700",children:"Clinic Name"}),u?e.jsx("input",{type:"text",value:n("name")||"",onChange:s=>r("name",s.target.value),className:"w-full rounded-lg border border-gray-300 px-3 py-2 text-gray-900 shadow-sm transition-colors focus:border-primaryBlue focus:outline-none focus:ring-2 focus:ring-primaryBlue/20",placeholder:"Enter clinic name"}):e.jsx("div",{className:"rounded-lg bg-gray-50 px-3 py-2 font-medium text-gray-900",children:a.name||"No name provided"})]}),e.jsxs("div",{className:"space-y-2",children:[e.jsx("label",{className:"text-sm font-medium text-gray-700",children:"Cost per Person"}),u?e.jsxs("div",{className:"relative",children:[e.jsx("div",{className:"absolute inset-y-0 left-0 flex items-center pl-3",children:e.jsx("span",{className:"font-medium text-gray-500",children:"$"})}),e.jsx("input",{type:"number",value:n("cost_per_head")||"",onChange:s=>r("cost_per_head",s.target.value),className:"w-full rounded-lg border border-gray-300 py-2 pl-8 pr-3 text-gray-900 shadow-sm transition-colors focus:border-primaryBlue focus:outline-none focus:ring-2 focus:ring-primaryBlue/20",placeholder:"0.00",min:"0",step:"0.01"})]}):e.jsx("div",{className:"rounded-lg bg-gray-50 px-3 py-2 font-medium text-gray-900",children:ge(a.cost_per_head)})]}),e.jsxs("div",{className:"space-y-2",children:[e.jsx("label",{className:"text-sm font-medium text-gray-700",children:"Description"}),u?e.jsx("textarea",{value:n("description")||"",onChange:s=>r("description",s.target.value),className:"w-full resize-none rounded-lg border border-gray-300 px-3 py-2 text-gray-900 shadow-sm transition-colors focus:border-primaryBlue focus:outline-none focus:ring-2 focus:ring-primaryBlue/20",rows:3,placeholder:"Enter clinic description"}):e.jsx("div",{className:"min-h-[80px] rounded-lg bg-gray-50 px-3 py-2 text-gray-900",children:a.description||"No description provided"})]})]}),e.jsxs("div",{className:"space-y-6",children:[e.jsx("div",{className:"border-b border-gray-200 pb-2",children:e.jsx("h4",{className:"text-sm font-semibold uppercase tracking-wide text-gray-900",children:"Sport Configuration"})}),e.jsxs("div",{className:"space-y-2",children:[e.jsx("label",{className:"text-sm font-medium text-gray-700",children:"Sport"}),u?e.jsx(I,{className:"w-full text-sm",options:g.filter(s=>s.status===1).map(s=>({value:s.id.toString(),label:s.name})),value:{value:f,label:((G=g.find(s=>s.id.toString()==f))==null?void 0:G.name)||"Select sport"},onChange:s=>{r("sport_id",s.value),r("type",""),r("sub_type","")},styles:c}):e.jsx("div",{className:"rounded-lg bg-gray-50 px-3 py-2 font-medium text-gray-900",children:((R=g.find(s=>s.id.toString()==a.sport_id))==null?void 0:R.name)||"No sport selected"})]}),e.jsxs("div",{className:"space-y-2",children:[e.jsx("label",{className:"text-sm font-medium text-gray-700",children:"Type"}),u?e.jsx(e.Fragment,{children:L.length>0?e.jsx(I,{className:"w-full text-sm",options:L.map(s=>({value:s.type,label:s.type})),value:{value:N,label:N||"Select type"},onChange:s=>{r("type",s.value),r("sub_type","")},styles:c}):e.jsx("div",{className:"rounded-lg bg-gray-100 px-3 py-2 text-sm italic text-gray-500",children:"This sport has no types"})}):e.jsx("div",{className:"rounded-lg bg-gray-50 px-3 py-2 font-medium text-gray-900",children:a.type||"No type selected"})]}),e.jsxs("div",{className:"space-y-2",children:[e.jsx("label",{className:"text-sm font-medium text-gray-700",children:"Sub-type"}),u?e.jsx(e.Fragment,{children:O.length>0?e.jsx(I,{className:"w-full text-sm",options:O.map(s=>({value:s,label:s})),value:{value:n("sub_type"),label:n("sub_type")||"Select sub-type"},onChange:s=>{r("sub_type",s.value)},styles:c}):e.jsx("div",{className:"rounded-lg bg-gray-100 px-3 py-2 text-sm italic text-gray-500",children:"This type has no sub-types"})}):e.jsx("div",{className:"rounded-lg bg-gray-50 px-3 py-2 font-medium text-gray-900",children:a.sub_type||"No sub-type selected"})]})]}),e.jsxs("div",{className:"space-y-6",children:[e.jsx("div",{className:"border-b border-gray-200 pb-2",children:e.jsx("h4",{className:"text-sm font-semibold uppercase tracking-wide text-gray-900",children:"Scheduling"})}),e.jsxs("div",{className:"grid grid-cols-2 gap-4",children:[e.jsxs("div",{className:"space-y-2",children:[e.jsx("label",{className:"text-sm font-medium text-gray-700",children:"Start Date"}),u?e.jsx("input",{type:"date",value:n("date")||"",onChange:s=>r("date",s.target.value),className:"w-full rounded-lg border border-gray-300 px-3 py-2 text-gray-900 shadow-sm transition-colors focus:border-primaryBlue focus:outline-none focus:ring-2 focus:ring-primaryBlue/20"}):e.jsx("div",{className:"rounded-lg bg-gray-50 px-3 py-2 font-medium text-gray-900",children:a.date?new Date(a.date).toLocaleDateString("en-US",{weekday:"long",year:"numeric",month:"long",day:"numeric"}):"No start date set"})]}),e.jsxs("div",{className:"space-y-2",children:[e.jsx("label",{className:"text-sm font-medium text-gray-700",children:"End Date"}),u?e.jsx("input",{type:"date",value:n("end_date")||"",onChange:s=>r("end_date",s.target.value),className:"w-full rounded-lg border border-gray-300 px-3 py-2 text-gray-900 shadow-sm transition-colors focus:border-primaryBlue focus:outline-none focus:ring-2 focus:ring-primaryBlue/20",min:n("date")||void 0}):e.jsx("div",{className:"rounded-lg bg-gray-50 px-3 py-2 font-medium text-gray-900",children:a.end_date?new Date(a.end_date).toLocaleDateString("en-US",{weekday:"long",year:"numeric",month:"long",day:"numeric"}):"No end date set"})]})]}),e.jsxs("div",{className:"grid grid-cols-2 gap-4",children:[e.jsxs("div",{className:"space-y-2",children:[e.jsx("label",{className:"text-sm font-medium text-gray-700",children:"Start Time"}),u?e.jsx(I,{className:"w-full text-sm",options:w,value:{value:n("start_time"),label:$(n("start_time"))||"Select time"},onChange:s=>{r("start_time",s.value)},placeholder:"Select start time",styles:c}):e.jsx("div",{className:"rounded-lg bg-gray-50 px-3 py-2 font-medium text-gray-900",children:$(a.start_time)||"Not set"})]}),e.jsxs("div",{className:"space-y-2",children:[e.jsx("label",{className:"text-sm font-medium text-gray-700",children:"End Time"}),u?e.jsx(I,{className:"w-full text-sm",options:re,value:{value:n("end_time"),label:$(n("end_time"))||"Select time"},onChange:s=>{r("end_time",s.value)},placeholder:_?"Select end time":"Select start time first",isDisabled:!_,styles:c}):e.jsx("div",{className:"rounded-lg bg-gray-50 px-3 py-2 font-medium text-gray-900",children:$(a.end_time)||"Not set"})]})]}),e.jsxs("div",{className:"space-y-2",children:[e.jsx("label",{className:"text-sm font-medium text-gray-700",children:"Recurring Event"}),u?e.jsxs("select",{value:n("recurring")===1||n("recurring")===!0?"Yes":"No",onChange:s=>r("recurring",s.target.value==="Yes"?1:0),className:"w-full rounded-lg border border-gray-300 px-3 py-2 text-gray-900 shadow-sm transition-colors focus:border-primaryBlue focus:outline-none focus:ring-2 focus:ring-primaryBlue/20",children:[e.jsx("option",{value:"No",children:"No"}),e.jsx("option",{value:"Yes",children:"Yes"})]}):e.jsx("div",{className:"rounded-lg bg-gray-50 px-3 py-2 font-medium text-gray-900",children:e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsx("div",{className:`h-2 w-2 rounded-full ${a.recurring===1?"bg-green-400":"bg-gray-400"}`}),e.jsx("span",{children:a.recurring===1?"Yes":"No"})]})})]})]}),e.jsxs("div",{className:"space-y-6",children:[e.jsx("div",{className:"border-b border-gray-200 pb-2",children:e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx("h4",{className:"text-sm font-semibold uppercase tracking-wide text-gray-900",children:"Coaches"}),e.jsxs("span",{className:"rounded-full bg-primaryBlue/10 px-2 py-1 text-xs font-semibold text-primaryBlue",children:[T.length," assigned"]})]})}),T.length>0?e.jsx("div",{className:"space-y-3",children:T.map(s=>{var l,d,t,p,de,J;return e.jsxs("div",{className:"flex items-center space-x-3 rounded-lg border border-gray-200 bg-gray-50 p-3 transition-all hover:shadow-sm",children:[e.jsx("div",{className:"h-10 w-10 overflow-hidden rounded-full shadow-sm ring-2 ring-white",children:e.jsx("img",{src:((l=s.user)==null?void 0:l.photo)||s.photo||"/default-avatar.png",alt:`${((d=s.user)==null?void 0:d.first_name)||""} ${((t=s.user)==null?void 0:t.last_name)||""}`,className:"h-full w-full object-cover",onError:C=>{C.target.src="/default-avatar.png"}})}),e.jsxs("div",{className:"flex-1",children:[e.jsxs("div",{className:"font-semibold text-gray-900",children:[(p=s.user)==null?void 0:p.first_name," ",(de=s.user)==null?void 0:de.last_name]}),((J=s.user)==null?void 0:J.email)&&e.jsx("div",{className:"text-sm text-gray-500",children:s.user.email})]}),e.jsxs("div",{className:"flex items-center space-x-1",children:[e.jsx("div",{className:"h-2 w-2 rounded-full bg-green-400"}),e.jsx("span",{className:"text-xs text-gray-500",children:"Active"})]})]},s.id)})}):e.jsxs("div",{className:"rounded-lg border-2 border-dashed border-gray-200 p-6 text-center",children:[e.jsx("div",{className:"mx-auto mb-3 flex h-12 w-12 items-center justify-center rounded-full bg-gray-100",children:e.jsx("svg",{className:"h-6 w-6 text-gray-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:e.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.25 2.25 0 11-4.5 0 2.25 2.25 0 014.5 0z"})})}),e.jsx("p",{className:"font-medium text-gray-500",children:"No coaches assigned"}),e.jsx("p",{className:"mt-1 text-sm text-gray-400",children:"Coaches will appear here when assigned to this clinic"})]})]})]}),Z&&e.jsx(je,{})]})," ",ee&&e.jsx(ze,{onClose:()=>{r("sport_id",y.sport_id),r("type",y.type),r("sub_type",y.sub_type),j(null),P(null),k(!1)},onConfirm:async s=>{const l=parseInt(s);j(l),console.log("Sport change option selected:",l),k(!1),ae&&await Y(ae),P(null)},eventCounts:A})]})};let me=new fe,Ve=new Fe;const Ke=[{header:"Clinic ID",accessor:"id"},{header:"Name",accessor:"name"},{header:"Max participants",accessor:"max_participants"},{header:"Start Date",accessor:"date"},{header:"End date",accessor:"end_date"},{header:"Time",accessor:"start_time"},{header:"Sport",accessor:"sport"},{header:"Fee",accessor:"cost_per_head"}],Ue=({club:o,sports:x,courts:b})=>{const{dispatch:h}=i.useContext(be),{dispatch:g}=i.useContext(ve),[a,r]=i.useState([]),[n,D]=i.useState(10),[E,u]=i.useState(0),[z,Z]=i.useState(0),[v,T]=i.useState(0),[H,ee]=i.useState(!1),[k,y]=i.useState(!1),[te,F]=i.useState(!1);i.useState([]),i.useState([]);const[j,A]=i.useState(!0),[se,ae]=i.useState(!1),[P,L]=i.useState(!1),B=we(),O=i.useRef(null),[V,re]=i.useState(!1),[M,f]=i.useState(null),[N,_]=i.useState(!1),[S,K]=i.useState(!1),[U,pe]=i.useState(!1);i.useState([]);const ne=Be({id:X(),email:X(),role:X(),status:X()});Ne({resolver:Oe(ne)});const le=t=>{t===""?c(1,n):c(1,n,{},[`courtmatchup_clinics.name,cs,${t}`])},oe=t=>{console.log("date search",t),t===""?c(1,n):c(1,n,{},[`date,cs,${t}`])};function ie(){c(v-1,n)}function Y(){c(v+1,n)}async function c(t,p,de={},J=[]){A(!(P||se));try{me.setTable("clinics");const C=await Ve.getPaginate("clinics",{page:t,limit:p,filter:[...J,`courtmatchup_clinics.club_id,eq,${o==null?void 0:o.id}`],join:["sports|sport_id"]});C&&A(!1);const{list:_e,total:Se,limit:Ce,num_pages:ue,page:ce}=C;r(_e),D(Ce),u(ue),T(ce),Z(Se),ee(ce>1),y(ce+1<=ue)}catch(C){A(!1),console.log("ERROR",C),he(g,C.message)}}const q=t=>{t.target.value===""?c(1,n):c(1,n,{},[`status,cs,${t.target.value}`])},G=t=>{const p=t.target.value;p===""?c(1,n):c(1,n,{},[`sport_id,eq,${p}`])};i.useEffect(()=>{if(h({type:"SETPATH",payload:{path:"program-clinics"}}),!(o!=null&&o.id))return;const p=setTimeout(async()=>{await c(1,n)},700);return()=>{clearTimeout(p)}},[o]);const R=t=>{O.current&&!O.current.contains(t.target)&&F(!1)};i.useEffect(()=>(document.addEventListener("mousedown",R),()=>{document.removeEventListener("mousedown",R)}),[]);const s=async t=>{K(!0);try{me.setTable("clinics"),await me.callRestAPI({id:t},"DELETE"),c(v,n)}catch(p){console.error("Error deleting clinic:",p),he(g,p.message)}finally{K(!1)}},l=t=>{const p={...t,id:t==null?void 0:t.id,date:t==null?void 0:t.date,startTime:t==null?void 0:t.start_time,endTime:t==null?void 0:t.end_time,sport_id:t==null?void 0:t.sport_id,type:t==null?void 0:t.type,sub_type:t==null?void 0:t.sub_type,reservation_type:3,price:t==null?void 0:t.price,status:t==null?void 0:t.status,player_ids:t==null?void 0:t.player_ids,coach_ids:t==null?void 0:t.coach_ids};f(p),re(!0)},d={status:t=>e.jsx("span",{className:`rounded-lg px-3 py-1 text-sm ${t.status===1?"bg-[#D1FAE5] text-[#065F46]":"bg-[#F4F4F4] text-[#393939]"}`,children:t.status===1?"Active":"Inactive"}),start_time:t=>$(t==null?void 0:t.start_time),date:t=>xe(t==null?void 0:t.date),end_date:t=>xe(t==null?void 0:t.end_date),players:t=>t!=null&&t.player_ids?`${JSON.parse(t==null?void 0:t.player_ids).length} players`:"0 players",sport:t=>{var p;return(p=t==null?void 0:t.sports)==null?void 0:p.name}};return e.jsxs("div",{className:"h-screen px-2 md:px-8",children:[S||j&&e.jsx(je,{}),e.jsx("div",{className:"flex flex-col gap-4 py-3",children:e.jsxs("div",{className:"flex flex-col gap-4 lg:flex-row lg:items-center lg:justify-between",children:[e.jsxs("div",{className:"flex flex-col gap-4 sm:flex-row sm:items-center",children:[e.jsxs("div",{className:"relative flex w-full max-w-xs flex-1 items-center",children:[e.jsx("div",{className:"pointer-events-none absolute inset-y-0 left-0 flex items-center pl-3",children:e.jsx(Ae,{className:"text-gray-500"})}),e.jsx("input",{type:"text",className:"block w-full rounded-md border border-gray-200 py-2 pl-10 pr-12 text-sm placeholder:text-gray-500 focus:border-blue-500 focus:outline-none focus:ring-1 focus:ring-blue-500",placeholder:"search clinics",onChange:t=>le(t.target.value)})]}),e.jsx("input",{type:"date",className:"w-full rounded-md border border-gray-200 text-sm text-gray-500 sm:w-auto",onChange:t=>oe(t.target.value)})]}),e.jsxs("div",{className:"flex flex-col gap-4 sm:flex-row sm:flex-wrap sm:items-center",children:[e.jsxs("select",{className:"w-full rounded-md border border-gray-200 px-3 py-2 pr-8 text-sm text-gray-700 sm:w-auto",defaultValue:"All",onChange:G,children:[e.jsx("option",{value:"",children:"Sport: All"}),x==null?void 0:x.map(t=>e.jsx("option",{value:t.id,children:t.name},t.id))]}),e.jsxs("select",{className:"w-full rounded-md border border-gray-200 px-3 py-2 pr-8 text-sm text-gray-700 sm:w-auto",defaultValue:"All",onChange:q,children:[e.jsx("option",{value:"",children:"Status: All"}),e.jsx("option",{value:"0",children:"Inactive"}),e.jsx("option",{value:"1",children:"Active"})]}),e.jsxs("div",{className:"flex items-center gap-4",children:[e.jsxs("button",{onClick:()=>B("/club/program-clinics/add"),className:"inline-flex items-center gap-2 rounded-md bg-[#1D275F] px-4 py-2 text-sm font-semibold text-white hover:bg-blue-700",children:[e.jsx("span",{children:"+"}),"Add new"]}),e.jsx(Ie,{title:"Clinic History",emptyMessage:"No clinic history found",activityType:Pe.clinic})]})]})]})}),j?e.jsx(Re,{}):e.jsx($e,{columns:Ke,data:a,loading:j,renderCustomCell:d,rowClassName:"hover:bg-gray-40 bg-gray-100 px-4 py-3 text-gray-500 cursor-pointer",emptyMessage:"No clinics available",loadingMessage:"Loading clinics...",onClick:t=>l(t)}),e.jsx(Me,{currentPage:v,pageCount:E,pageSize:n,canPreviousPage:H,canNextPage:k,updatePageSize:t=>{D(t),c(1,t)},previousPage:ie,nextPage:Y,gotoPage:t=>c(t,n)}),e.jsx(Le,{isOpen:N,onClose:()=>_(!1),onDelete:s,loading:U,title:"Delete",message:"Are you sure you want to delete this clinic?"}),e.jsx(He,{getData:c,onClose:()=>f(null),clinic:M,isOpen:M!==null,sports:x})]})},rt=Ue;export{rt as L};
