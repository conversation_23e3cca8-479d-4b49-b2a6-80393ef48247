import{j as e}from"./@nivo/heatmap-ba1ecfff.js";import{R as z,a3 as p,G as q,M as K,e as U,I as V,aH as Q}from"./index-12adfaa3.js";import{r as c,b as X}from"./vendor-851db8c1.js";import{f as x,o as Z}from"./date-fns-07266b7d.js";import{C as ee}from"./ChevronLeftIcon-e5eecf9c.js";import{C as se}from"./ChevronRightIcon-efb4c46c.js";import"./@craftjs/core-d3c11b68.js";import"./@fortawesome/react-fontawesome-13437837.js";import"./@fortawesome/fontawesome-svg-core-4fa3e289.js";import"./react-confirm-alert-cd7ccfe7.js";import"./@tanstack/react-query-20158223.js";import"./@stripe/stripe-js-6b714a86.js";import"./moment-a9aaa855.js";import"./cal-heatmap-cf010ec4.js";import"./react-icons-51bc3cff.js";import"./smoothscroll-polyfill-a5c0a116.js";import"./lodash-91d5d207.js";import"./numeral-ea653b2a.js";import"./@stripe/react-stripe-js-64f0e61f.js";import"./react-hook-form-687afde5.js";import"./react-select-c8303602.js";import"./@mantine/core-8cbffb6d.js";import"./@emotion/react-89b506c3.js";import"./@emotion/cache-9a5b99cd.js";import"./@emotion/utils-8a8f62c5.js";import"./@emotion/serialize-460cad7f.js";import"./@uppy/dashboard-4a19149e.js";import"./@fullcalendar/core-8ccc1ac4.js";import"./@uppy/core-0760343f.js";import"./@uppy/aws-s3-c5961f7a.js";import"./@uppy/compressor-11f993e4.js";import"./@headlessui/react-a5400090.js";import"./@fortawesome/free-solid-svg-icons-0a9c4907.js";import"./@fortawesome/free-regular-svg-icons-0a88e957.js";import"./@fortawesome/free-brands-svg-icons-fae0dcac.js";function te({isOpen:y,onClose:a,booking:s}){return s?(s.player_ids&&JSON.parse(s.player_ids),e.jsx(z,{isOpen:y,onClose:a,title:"Booking Details",showFooter:!1,children:e.jsxs("div",{className:"space-y-6",children:[e.jsxs("div",{children:[e.jsx("p",{className:"text-sm text-gray-500",children:"Booking Type"}),e.jsx("p",{className:"mt-1 font-medium capitalize",children:s.booking_type})]}),e.jsxs("div",{children:[e.jsx("p",{className:"text-sm text-gray-500",children:"Date & Time"}),e.jsxs("p",{className:"mt-1 font-medium",children:[x(new Date(s.booking_date),"MMMM d, yyyy")," •"," ",p(s.start_time)," -"," ",p(s.end_time)]})]}),e.jsxs("div",{children:[e.jsx("p",{className:"text-sm text-gray-500",children:"Duration"}),e.jsxs("p",{className:"mt-1 font-medium",children:[s.duration," hour(s)"]})]}),e.jsxs("div",{children:[e.jsx("p",{className:"text-sm text-gray-500",children:"Number of Players"}),e.jsx("p",{className:"mt-1 font-medium",children:s.num_players})]}),s.price>0&&e.jsxs("div",{children:[e.jsx("p",{className:"text-sm text-gray-500",children:"Price"}),e.jsxs("p",{className:"mt-1 font-medium",children:["$",s.price.toFixed(2)]})]}),s.notes&&e.jsxs("div",{children:[e.jsx("p",{className:"text-sm text-gray-500",children:"Notes"}),e.jsx("p",{className:"mt-1 font-medium",children:s.notes})]}),s.clinic_name&&e.jsxs(e.Fragment,{children:[e.jsxs("div",{children:[e.jsx("p",{className:"text-sm text-gray-500",children:"Clinic Name"}),e.jsx("p",{className:"mt-1 font-medium",children:s.clinic_name})]}),s.clinic_description&&e.jsxs("div",{children:[e.jsx("p",{className:"text-sm text-gray-500",children:"Clinic Description"}),e.jsx("p",{className:"mt-1 font-medium",children:s.clinic_description})]}),s.clinic_level&&e.jsxs("div",{children:[e.jsx("p",{className:"text-sm text-gray-500",children:"Clinic Level"}),e.jsx("p",{className:"mt-1 font-medium",children:s.clinic_level})]}),s.max_participants&&e.jsxs("div",{children:[e.jsx("p",{className:"text-sm text-gray-500",children:"Maximum Participants"}),e.jsx("p",{className:"mt-1 font-medium",children:s.max_participants})]})]})]})})):null}let g=new K;function Ye(){const{dispatch:y}=c.useContext(q),[a,s]=c.useState(new Date),[m,_]=c.useState(new Date),[w,R]=c.useState([]),[ae,T]=c.useState(null),[f,I]=c.useState(null),[F,v]=c.useState(!0),[L,b]=c.useState(null),[O,M]=c.useState(!1);X.useEffect(()=>{y({type:"SETPATH",payload:{path:"club-calendar"}})},[]);const k=()=>{s(new Date(a.setMonth(a.getMonth()-1)))},B=localStorage.getItem("user"),P=async()=>{v(!0);try{g.setTable("user");const i=await g.callRestAPI({id:B},"GET");g.setTable("clubs");const l=await g.callRestAPI({id:i.model.club_id},"GET"),r=await g.callRawAPI("/v3/api/custom/courtmatchup/user/reservations",{},"GET");R(r.list),T(l.model),I(l.model)}catch(i){console.log(i)}finally{v(!1)}},A=()=>{s(new Date(a.setMonth(a.getMonth()+1)))},C=i=>{if(!f)return!1;const l=x(i,"EEEE");return JSON.parse(f.days_off||"[]").includes(l)},D=i=>{if(!f)return[];const l=x(i,"EEEE").toLowerCase();return JSON.parse(f.exceptions||"[]").reduce((n,o)=>{const d=o.days.find(h=>h.day===l);return d&&n.push({name:o.name,timeslots:d.timeslots}),n},[])},S=i=>{if(!w)return[];const l=x(i,"yyyy-MM-dd");return w.filter(r=>r.booking_date===l)},Y=()=>{const i=new Date(a.getFullYear(),a.getMonth(),1),l=new Date(a.getFullYear(),a.getMonth()+1,0);let r=i.getDay();r=r===0?6:r-1;const n=l.getDate(),o=["M","T","W","T","F","S","S"],d=Array(r).fill(null),h=Array.from({length:n},(t,j)=>j+1),H=[...d,...h],u=t=>!m||!t?!1:m.getDate()===t&&m.getMonth()===a.getMonth()&&m.getFullYear()===a.getFullYear(),J=t=>{t&&_(new Date(a.getFullYear(),a.getMonth(),t))};return e.jsxs("div",{className:"w-full",children:[F&&e.jsx(U,{}),e.jsxs("div",{className:"mb-3 flex items-center justify-between rounded-lg bg-gray-50 p-1.5 sm:mb-4 sm:p-2",children:[e.jsx(ee,{className:"h-4 w-4 cursor-pointer text-gray-600 hover:text-gray-800 sm:h-5 sm:w-5",onClick:k}),e.jsxs("p",{className:"text-sm font-medium sm:text-base",children:[a.toLocaleString("default",{month:"long"})," ",a.getFullYear()]}),e.jsx(se,{className:"h-4 w-4 cursor-pointer text-gray-600 hover:text-gray-800 sm:h-5 sm:w-5",onClick:A})]}),e.jsxs("div",{className:"grid grid-cols-7 gap-1 sm:gap-2",children:[o.map(t=>e.jsx("div",{className:"text-center text-xs font-medium text-gray-500 sm:text-sm",children:t},t)),H.map((t,j)=>{if(!t)return e.jsx("div",{className:"cursor-default"},j);const N=new Date(a.getFullYear(),a.getMonth(),t),E=C(N),W=S(N).length>0,$=D(N).length>0;return e.jsxs("div",{onClick:()=>J(t),className:`
                  relative m-0 h-6 cursor-pointer rounded-lg p-1 text-center text-xs sm:h-8 sm:text-sm

                  ${u(t)?"bg-green-700 text-white hover:bg-green-800":""}
                  ${Z(N)&&!u(t)?"border border-green-700":""}
                `,children:[t,e.jsxs("div",{className:"absolute bottom-0 left-1/2 flex -translate-x-1/2 gap-0.5 sm:gap-1",children:[W&&!u(t)&&e.jsx("div",{className:"h-1 w-1 rounded-full bg-blue-500"}),E&&!u(t)&&e.jsx("div",{className:"h-1 w-1 rounded-full bg-red-500"}),$&&!E&&!u(t)&&e.jsx("div",{className:"h-1 w-1 rounded-full bg-yellow-500"})]})]},j)})]})]})},G=()=>{if(!m)return null;const i=C(m),l=S(m),r=D(m);return e.jsxs("div",{className:"h-fit w-full rounded-xl bg-white shadow-sm sm:shadow-lg",children:[e.jsx("div",{className:"rounded-t-lg bg-gray-50 p-3 text-center sm:p-4",children:e.jsx("p",{className:"text-sm text-gray-500 sm:text-base",children:x(m,"EEEE, MMMM d, yyyy")})}),e.jsx("div",{className:"p-3 sm:p-4 md:p-6",children:i?e.jsxs("div",{className:"flex items-center justify-center gap-2 text-red-500",children:[e.jsx(V,{className:"h-4 w-4 sm:h-5 sm:w-5"}),e.jsx("p",{className:"text-sm sm:text-base",children:"Club is closed on this day"})]}):e.jsxs("div",{className:"space-y-4 sm:space-y-6",children:[r.length>0&&e.jsxs("div",{className:"space-y-2 sm:space-y-3",children:[e.jsx("p",{className:"text-sm font-medium text-yellow-600 sm:text-base",children:"Exceptions:"}),r.map((n,o)=>e.jsx("div",{className:"flex items-center gap-2 rounded-lg border border-yellow-200 bg-yellow-50 p-2 sm:gap-3 sm:p-3",children:e.jsxs("div",{children:[e.jsx("p",{className:"text-sm font-medium capitalize sm:text-base",children:n.name}),e.jsx("div",{className:"flex flex-wrap gap-1 sm:gap-2",children:Q(n.timeslots).map((d,h)=>e.jsxs("span",{className:"text-xs text-gray-500 sm:text-sm",children:[p(d.start)," -"," ",p(d.end)]},h))})]})},o))]}),l.length>0&&e.jsxs("div",{className:"space-y-2 sm:space-y-3",children:[e.jsx("p",{className:"text-sm font-medium sm:text-base",children:"Reservations:"}),l.map((n,o)=>e.jsxs("div",{className:"flex flex-col justify-between gap-1 rounded-lg border border-gray-100 bg-gray-100 p-2 shadow-sm sm:gap-2 sm:p-3",children:[e.jsx("div",{className:"flex items-center justify-between gap-2 sm:gap-3",children:e.jsx("h3",{className:"text-sm font-medium sm:text-base",children:n.booking_type})}),e.jsxs("div",{className:"flex flex-wrap items-end justify-between gap-1 sm:flex-nowrap sm:gap-2",children:[e.jsxs("p",{className:"text-xs text-gray-600 sm:text-sm",children:[x(new Date(n.booking_date),"MMMM d")," • ",p(n.start_time)," -"," ",p(n.end_time)]}),e.jsxs("button",{onClick:()=>{b(n),M(!0)},className:"flex items-center justify-center whitespace-nowrap text-xs font-medium text-blue-600 hover:text-blue-800 sm:text-sm",children:["Details"," ",e.jsxs("svg",{width:"14",height:"14",viewBox:"0 0 24 24",role:"img",xmlns:"http://www.w3.org/2000/svg","aria-labelledby":"arrowRightTopIconTitle",stroke:"currentColor",strokeWidth:"1",strokeLinecap:"square",strokeLinejoin:"miter",fill:"none",color:"currentColor",className:"ml-1",children:[e.jsx("title",{id:"arrowRightTopIconTitle",children:"Arrow Right Top"}),e.jsx("path",{d:"M19 13V5h-8"}),e.jsx("path",{strokeLinecap:"round",d:"M19 5l-1 1"}),e.jsx("path",{d:"M18 6L5 19"})]})]})]})]},o))]}),r.length===0&&l.length===0&&e.jsx("div",{className:"flex items-center justify-center py-4",children:e.jsx("p",{className:"text-sm text-gray-500 sm:text-base",children:"No events scheduled"})})]})})]})};return c.useEffect(()=>{P()},[]),e.jsxs("div",{children:[e.jsx("div",{className:"flex items-center justify-between bg-white px-3 py-3 sm:px-4 sm:py-4",children:e.jsx("h1",{className:"text-lg font-semibold sm:mb-0 sm:text-xl",children:"Club Calendar"})}),e.jsx("div",{className:"mx-auto p-3 sm:p-4 md:p-5",children:e.jsxs("div",{className:"flex flex-col gap-4 md:flex-row md:gap-5 lg:mx-auto lg:max-w-4xl",children:[e.jsxs("div",{className:"w-full md:w-auto md:min-w-[280px] md:max-w-[300px]",children:[e.jsx("div",{className:"rounded-lg bg-white p-3 shadow-sm sm:p-4 sm:shadow-5",children:Y()}),e.jsxs("div",{className:"mt-3 flex flex-wrap gap-2 sm:mt-4 sm:gap-3",children:[e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx("div",{className:"h-2 w-2 rounded-lg bg-red-500"}),e.jsx("p",{className:"text-xs text-gray-500 sm:text-sm",children:"Club closed"})]}),e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx("div",{className:"h-2 w-2 rounded-lg bg-blue-500"}),e.jsx("p",{className:"text-xs text-gray-500 sm:text-sm",children:"Reservations"})]}),e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx("div",{className:"h-2 w-2 rounded-lg bg-yellow-500"}),e.jsx("p",{className:"text-xs text-gray-500 sm:text-sm",children:"Exceptions"})]})]})]}),e.jsx("div",{className:"w-full flex-1",children:G()})]})}),e.jsx(te,{isOpen:O,onClose:()=>{M(!1),b(null)},booking:L})]})}export{Ye as default};
