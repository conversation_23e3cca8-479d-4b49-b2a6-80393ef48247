import{j as e}from"./@nivo/heatmap-ba1ecfff.js";import{b as r,f as N}from"./vendor-851db8c1.js";import{u as _}from"./react-hook-form-687afde5.js";import{o as A}from"./yup-2824f222.js";import{c as E,a as c}from"./yup-54691517.js";import{G as k,A as D,d as O,M as R,b as T,t as C}from"./index-12adfaa3.js";import"./react-quill-73fb9518.js";/* empty css                   */import{M as g}from"./MkdInput-3424cacd.js";import"./index-02625b16.js";import"./@craftjs/core-d3c11b68.js";import"./@fortawesome/react-fontawesome-13437837.js";import"./@fortawesome/fontawesome-svg-core-4fa3e289.js";import"./@hookform/resolvers-67648cca.js";import"./react-confirm-alert-cd7ccfe7.js";import"./@tanstack/react-query-20158223.js";import"./@stripe/stripe-js-6b714a86.js";import"./moment-a9aaa855.js";import"./cal-heatmap-cf010ec4.js";import"./react-icons-51bc3cff.js";import"./smoothscroll-polyfill-a5c0a116.js";import"./date-fns-07266b7d.js";import"./lodash-91d5d207.js";import"./numeral-ea653b2a.js";import"./@stripe/react-stripe-js-64f0e61f.js";import"./react-select-c8303602.js";import"./@mantine/core-8cbffb6d.js";import"./@emotion/react-89b506c3.js";import"./@emotion/cache-9a5b99cd.js";import"./@emotion/utils-8a8f62c5.js";import"./@emotion/serialize-460cad7f.js";import"./@uppy/dashboard-4a19149e.js";import"./@fullcalendar/core-8ccc1ac4.js";import"./@uppy/core-0760343f.js";import"./@uppy/aws-s3-c5961f7a.js";import"./@uppy/compressor-11f993e4.js";import"./@headlessui/react-a5400090.js";import"./@fortawesome/free-solid-svg-icons-0a9c4907.js";import"./@fortawesome/free-regular-svg-icons-0a88e957.js";import"./@fortawesome/free-brands-svg-icons-fae0dcac.js";const we=({setSidebar:j})=>{var x,h;const{dispatch:l}=r.useContext(k),y=E({user_id:c(),stripe_id:c(),object:c()}).required(),{dispatch:S}=r.useContext(D),[u,F]=r.useState({}),[b,p]=r.useState(!1),v=N(),{register:d,handleSubmit:w,setError:f,setValue:M,formState:{errors:i}}=_({resolver:A(y)});r.useState([]);const I=async a=>{let n=new R;p(!0);try{for(let s in u){let o=new FormData;o.append("file",u[s].file);let m=await n.uploadImage(o);a[s]=m.url}n.setTable("stripe_invoice");const t=await n.callRestAPI({user_id:a.user_id,stripe_id:a.stripe_id,object:a.object},"POST");if(!t.error)T(l,"Added"),v("/club/stripe_invoice"),j(!1),l({type:"REFRESH_DATA",payload:{refreshData:!0}});else if(t.validation){const s=Object.keys(t.validation);for(let o=0;o<s.length;o++){const m=s[o];f(m,{type:"manual",message:t.validation[m]})}}p(!1)}catch(t){p(!1),console.log("Error",t),f("user_id",{type:"manual",message:t.message}),C(S,t.message)}};return r.useEffect(()=>{l({type:"SETPATH",payload:{path:"stripe_invoice"}})},[]),e.jsxs("div",{className:" shadow-md rounded  mx-auto p-5",children:[e.jsx("h4",{className:"text-2xl font-medium",children:"Add Stripe Invoice"}),e.jsxs("form",{className:" w-full max-w-lg",onSubmit:w(I),children:[e.jsx(g,{type:"number",page:"add",name:"user_id",errors:i,label:"User Id",placeholder:"User Id",register:d,className:""}),e.jsx(g,{type:"text",page:"add",name:"stripe_id",errors:i,label:"Stripe Id",placeholder:"Stripe Id",register:d,className:""}),e.jsxs("div",{className:"mb-4  ",children:[e.jsx("label",{className:"block text-gray-700 text-sm font-bold mb-2",htmlFor:"object",children:"Object"}),e.jsx("textarea",{placeholder:"Object",...d("object"),className:`shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline ${(x=i.object)!=null&&x.message?"border-red-500":""}`,row:50}),e.jsx("p",{className:"text-red-500 text-xs italic",children:(h=i.object)==null?void 0:h.message})]}),e.jsx(O,{type:"submit",loading:b,disabled:b,className:"bg-primaryBlue text-white font-bold py-2 px-4 rounded focus:outline-none focus:shadow-outline",children:"Submit"})]})]})};export{we as default};
