import{j as e}from"./@nivo/heatmap-ba1ecfff.js";import{b as o,f as T}from"./vendor-851db8c1.js";import{u as F}from"./react-hook-form-687afde5.js";import{o as D}from"./yup-2824f222.js";import{c as R,a as n}from"./yup-54691517.js";import{G as C,A as I,d as $,M as H,b as M,t as O}from"./index-12adfaa3.js";import"./react-quill-73fb9518.js";/* empty css                   */import{M as P}from"./MkdInput-3424cacd.js";import"./index-02625b16.js";import"./@craftjs/core-d3c11b68.js";import"./@fortawesome/react-fontawesome-13437837.js";import"./@fortawesome/fontawesome-svg-core-4fa3e289.js";import"./@hookform/resolvers-67648cca.js";import"./react-confirm-alert-cd7ccfe7.js";import"./@tanstack/react-query-20158223.js";import"./@stripe/stripe-js-6b714a86.js";import"./moment-a9aaa855.js";import"./cal-heatmap-cf010ec4.js";import"./react-icons-51bc3cff.js";import"./smoothscroll-polyfill-a5c0a116.js";import"./date-fns-07266b7d.js";import"./lodash-91d5d207.js";import"./numeral-ea653b2a.js";import"./@stripe/react-stripe-js-64f0e61f.js";import"./react-select-c8303602.js";import"./@mantine/core-8cbffb6d.js";import"./@emotion/react-89b506c3.js";import"./@emotion/cache-9a5b99cd.js";import"./@emotion/utils-8a8f62c5.js";import"./@emotion/serialize-460cad7f.js";import"./@uppy/dashboard-4a19149e.js";import"./@fullcalendar/core-8ccc1ac4.js";import"./@uppy/core-0760343f.js";import"./@uppy/aws-s3-c5961f7a.js";import"./@uppy/compressor-11f993e4.js";import"./@headlessui/react-a5400090.js";import"./@fortawesome/free-solid-svg-icons-0a9c4907.js";import"./@fortawesome/free-regular-svg-icons-0a88e957.js";import"./@fortawesome/free-brands-svg-icons-fae0dcac.js";const Ae=({setSidebar:N})=>{var b,h,f,j,y,w;const{dispatch:c}=o.useContext(C),S=R({slug:n(),subject:n(),tag:n(),html:n()}).required(),{dispatch:v}=o.useContext(I),[u,B]=o.useState({}),[x,d]=o.useState(!1),E=T(),{register:i,handleSubmit:k,setError:g,setValue:G,formState:{errors:a}}=F({resolver:D(S)});o.useState([]);const A=async r=>{let p=new H;d(!0);try{for(let l in u){let s=new FormData;s.append("file",u[l].file);let m=await p.uploadImage(s);r[l]=m.url}p.setTable("email");const t=await p.callRestAPI({slug:r.slug,subject:r.subject,tag:r.tag,html:r.html},"POST");if(!t.error)M(c,"Added"),E("/club/email"),N(!1),c({type:"REFRESH_DATA",payload:{refreshData:!0}});else if(t.validation){const l=Object.keys(t.validation);for(let s=0;s<l.length;s++){const m=l[s];g(m,{type:"manual",message:t.validation[m]})}}d(!1)}catch(t){d(!1),console.log("Error",t),g("slug",{type:"manual",message:t.message}),O(v,t.message)}};return o.useEffect(()=>{c({type:"SETPATH",payload:{path:"email"}})},[]),e.jsxs("div",{className:" shadow-md rounded  mx-auto p-5",children:[e.jsx("h4",{className:"text-2xl font-medium",children:"Add Email"}),e.jsxs("form",{className:" w-full max-w-lg",onSubmit:k(A),children:[e.jsx(P,{type:"text",page:"add",name:"slug",errors:a,label:"Slug",placeholder:"Slug",register:i,className:""}),e.jsxs("div",{className:"mb-4  ",children:[e.jsx("label",{className:"block text-gray-700 text-sm font-bold mb-2",htmlFor:"subject",children:"Subject"}),e.jsx("textarea",{placeholder:"Subject",...i("subject"),className:`shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline ${(b=a.subject)!=null&&b.message?"border-red-500":""}`,row:50}),e.jsx("p",{className:"text-red-500 text-xs italic",children:(h=a.subject)==null?void 0:h.message})]}),e.jsxs("div",{className:"mb-4  ",children:[e.jsx("label",{className:"block text-gray-700 text-sm font-bold mb-2",htmlFor:"tag",children:"Tag"}),e.jsx("textarea",{placeholder:"Tag",...i("tag"),className:`shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline ${(f=a.tag)!=null&&f.message?"border-red-500":""}`,row:50}),e.jsx("p",{className:"text-red-500 text-xs italic",children:(j=a.tag)==null?void 0:j.message})]}),e.jsxs("div",{className:"mb-4  ",children:[e.jsx("label",{className:"block text-gray-700 text-sm font-bold mb-2",htmlFor:"html",children:"Html"}),e.jsx("textarea",{placeholder:"Html",...i("html"),className:`shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline ${(y=a.html)!=null&&y.message?"border-red-500":""}`,row:50}),e.jsx("p",{className:"text-red-500 text-xs italic",children:(w=a.html)==null?void 0:w.message})]}),e.jsx($,{type:"submit",loading:x,disabled:x,className:"bg-primaryBlue text-white font-bold py-2 px-4 rounded focus:outline-none focus:shadow-outline",children:"Submit"})]})]})};export{Ae as default};
