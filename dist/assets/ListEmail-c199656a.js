import{j as e}from"./@nivo/heatmap-ba1ecfff.js";import{b as s,f as ne}from"./vendor-851db8c1.js";import{M as ie,T as ce,G as ue,A as de,c as me,K as pe,R as fe,t as xe,b as p}from"./index-12adfaa3.js";import"./index-be4468eb.js";import{c as he,a as h}from"./yup-54691517.js";import{u as ge}from"./react-hook-form-687afde5.js";import{o as be}from"./yup-2824f222.js";import"./AddButton.module-98aac587.js";import{P as ye}from"./index-eb1bc208.js";import"./lodash-91d5d207.js";import je from"./Skeleton-1e8bf077.js";import{E as we}from"./EmailTemplateDrawer-9a632321.js";import{H as Se}from"./HistoryComponent-90e771dd.js";let r=new ie;new ce;const ve=[{header:"Subject",accessor:"subject",isSorted:!1,isSortedDesc:!1,mappingExist:!1,mappings:{}},{header:"Action",accessor:""}],Ne=()=>{const{dispatch:u}=s.useContext(ue),{dispatch:L}=s.useContext(de),[d,f]=s.useState([]),[n,E]=s.useState(10),[R,I]=s.useState(0),[Ee,O]=s.useState(0),[g,H]=s.useState(0),[B,F]=s.useState(!1),[G,V]=s.useState(!1),[Ce,$]=s.useState(!1);s.useState(!1),s.useState([]),s.useState([]),s.useState("eq");const[b,y]=s.useState(!0),[q,Te]=s.useState(!1),[K,Pe]=s.useState(!1);s.useState(),ne();const C=s.useRef(null),[z,T]=s.useState(!1),[Me,U]=s.useState(!1),[_,j]=s.useState(null),[Z,J]=s.useState([]),[Q,x]=s.useState(!1),[a,m]=s.useState(null),[W,P]=s.useState(!1),[X,w]=s.useState(!1),[Y,M]=s.useState(!1),ee=he({id:h(),email:h(),role:h(),status:h()});ge({resolver:be(ee)});function te(){i(g-1,n)}function se(){i(g+1,n)}async function i(t,l,o={},S=[]){y(!(K||q));try{r.setTable("email");const c=await r.callRestAPI({payload:{...o},page:t,limit:l},"PAGINATE");c&&y(!1);const{list:v,total:re,limit:oe,num_pages:A,page:N}=c;f(v),E(oe),I(A),H(N),O(re),F(N>1),V(N+1<=A)}catch(c){y(!1),console.log("ERROR",c),xe(L,c.message)}}const ae=async()=>{try{r.setTable("user");const t=await r.callRestAPI({filter:["role,cs,user"]},"GETALL");r.setTable("profile");const l=await r.callRestAPI({},"GETALL"),o=t.list.map(S=>{const c=l.list.find(v=>v.user_id===S.id);return{...S,...c}});J(o)}catch(t){return console.error("Error fetching users:",t),[]}finally{}},k=async t=>{t.preventDefault(),P(!0);try{if(a!=null&&a.id)r.setTable("email"),(await r.callRestAPI({subject:a.subject,html:a.html,id:a.id},"PUT")).error||(p(u,"Email template updated successfully!",3e3,"success"),f(d.map(o=>o.id===a.id?{...o,...a}:o)));else{r.setTable("email");const l=await r.callRestAPI({subject:a.subject,html:a.html},"POST");l.error||(p(u,"Email template created successfully!",3e3,"success"),f([{...a,id:l.data},...d]))}x(!1),m(null)}catch(l){console.error(l),p(u,"Error creating email template",3e3,"error")}finally{P(!1)}},le=async t=>{M(!0);try{r.setTable("email"),(await r.callRestAPI({id:t},"DELETE")).error||(p(u,"Email template deleted successfully!",3e3,"success"),f(d.filter(o=>o.id!==t)))}catch(l){console.error(l),p(u,"An error occurred while deleting the template.",3e3,"error")}finally{M(!1),U(!1),j(null),w(!1)}};s.useEffect(()=>{u({type:"SETPATH",payload:{path:"email"}}),ae();const l=setTimeout(async()=>{await i(1,n)},700);return()=>{clearTimeout(l)}},[]);const D=t=>{C.current&&!C.current.contains(t.target)&&$(!1)};return s.useEffect(()=>(document.addEventListener("mousedown",D),()=>{document.removeEventListener("mousedown",D)}),[]),e.jsxs("div",{className:"h-screen px-8",children:[e.jsxs("div",{className:"flex flex-col justify-between gap-4 py-3 md:flex-row md:items-center",children:[e.jsxs("div",{className:"relative flex max-w-[300px] flex-1 items-center justify-between",children:[e.jsx("div",{className:"pointer-events-none absolute inset-y-0 left-0 flex items-center pl-3",children:e.jsx(me,{className:"text-gray-500"})}),e.jsx("input",{type:"text",className:"block w-full rounded-xl border border-gray-200 py-2 pl-10 pr-12 text-sm placeholder:text-gray-500 focus:border-blue-500 focus:outline-none focus:ring-1 focus:ring-blue-500",placeholder:"search email subject",onChange:t=>{t.target.value?i(0,n,{},[`subject,cs,${t.target.value}`]):i(0,n)}})]}),e.jsxs("div",{className:"flex items-center gap-4",children:[e.jsxs("button",{onClick:()=>T(!0),className:"inline-flex items-center gap-2 rounded-xl border border-gray-200 bg-white px-4 py-2 text-sm font-semibold text-gray-500 hover:bg-gray-100",children:[e.jsx("svg",{width:"20",height:"20",viewBox:"0 0 20 20",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:e.jsx("path",{d:"M4.99919 10.0017H7.70752M4.99919 10.0017L2.81729 3.45596C2.6999 3.1038 3.06689 2.78551 3.39891 2.95152L16.7538 9.62898C17.0609 9.78253 17.0609 10.2208 16.7538 10.3743L3.39891 17.0518C3.06689 17.2178 2.6999 16.8995 2.81729 16.5474L4.99919 10.0017Z",stroke:"#868C98","stroke-width":"1.5","stroke-linecap":"round","stroke-linejoin":"round"})}),"Send custom email"]}),e.jsx(Se,{title:"Email History",emptyMessage:"No email history found"}),e.jsxs("button",{onClick:()=>{m(null),x(!0)},className:"inline-flex items-center gap-2 rounded-xl bg-[#1D275F] px-4 py-2 text-sm font-semibold text-white hover:bg-blue-700",children:[e.jsx("span",{children:"+"}),"Add new"]})]})]}),b?e.jsx(je,{}):e.jsxs("div",{className:"overflow-x-auto",children:[e.jsxs("table",{className:"w-full min-w-full table-auto border-separate border-spacing-y-2",children:[e.jsx("thead",{className:"!border-none",children:e.jsx("tr",{children:ve.map((t,l)=>t.accessor===""?e.jsx("th",{scope:"col",className:"whitespace-nowrap px-6 py-4 text-left text-sm font-medium text-gray-500"},l):e.jsx("th",{scope:"col",className:"whitespace-nowrap px-6 py-4 text-left text-sm font-medium text-gray-500",children:t.header},l))})}),e.jsx("tbody",{className:"",children:d.map((t,l)=>e.jsxs("tr",{className:" cursor-pointer bg-gray-100 px-4 py-3 hover:bg-gray-50",onClick:()=>{m(t),x(!0)},children:[e.jsx("td",{className:"whitespace-nowrap px-6 py-4",children:e.jsx("div",{className:"flex items-center",children:e.jsx("div",{className:"text-sm font-medium text-gray-900",children:t.subject})})}),e.jsx("td",{className:"flex justify-end whitespace-nowrap px-6 py-4",children:e.jsx("div",{className:"flex justify-end gap-4",children:e.jsx("button",{onClick:o=>{o.stopPropagation(),j(t.id),w(!0)},className:"text-sm font-medium text-red-600 hover:text-red-800",children:e.jsx("svg",{width:"24",height:"24",viewBox:"0 0 24 24",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:e.jsx("path",{d:"M6.79167 6.79297V18.8763C6.79167 19.3365 7.16476 19.7096 7.625 19.7096H16.375C16.8352 19.7096 17.2083 19.3365 17.2083 18.8763V6.79297M6.79167 6.79297H17.2083M6.79167 6.79297H5.125M17.2083 6.79297H18.875M13.6667 10.9596V15.543M10.3333 10.9596V15.543M9.5 6.79297C9.5 5.41226 10.6193 4.29297 12 4.29297C13.3807 4.29297 14.5 5.41226 14.5 6.79297",stroke:"#868C98","stroke-width":"1.5","stroke-linecap":"round","stroke-linejoin":"round"})})})})})]},l))})]}),b&&e.jsx("div",{className:"px-6 py-4",children:e.jsx("p",{className:"text-gray-500",children:"Loading..."})}),!b&&d.length===0&&e.jsx("div",{className:"w-full px-6 py-4 text-center",children:e.jsx("p",{className:"text-gray-500",children:"No data available"})})]}),e.jsx(ye,{currentPage:g,pageCount:R,pageSize:n,canPreviousPage:B,canNextPage:G,updatePageSize:t=>{E(t),i(1,t)},previousPage:te,nextPage:se,gotoPage:t=>i(t,n)}),e.jsx(we,{isOpen:z,onClose:()=>T(!1),members:Z}),e.jsx(pe,{isOpen:X,onClose:()=>{w(!1),j(null)},onDelete:()=>le(_),loading:Y}),e.jsx(fe,{isOpen:Q,onClose:()=>x(!1),title:a?"Edit Email Template":"Add New Email Template",primaryButtonText:a?"Update Template":"Create Template",onPrimaryAction:k,submitting:W,children:e.jsxs("form",{onSubmit:k,className:"space-y-6 py-6",children:[e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700",children:"Subject"}),e.jsx("input",{type:"text",name:"subject",value:(a==null?void 0:a.subject)||"",onChange:t=>m({...a,subject:t.target.value}),className:"mt-1 block w-full rounded-md border border-gray-300 px-3 py-2 shadow-sm focus:border-blue-500 focus:outline-none focus:ring-1 focus:ring-blue-500",required:!0})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700",children:"Message"}),e.jsx("textarea",{name:"message",value:(a==null?void 0:a.html)||"",onChange:t=>m({...a,html:t.target.value}),rows:6,className:"mt-1 block w-full rounded-md border border-gray-300 px-3 py-2 shadow-sm focus:border-blue-500 focus:outline-none focus:ring-1 focus:ring-blue-500",required:!0})]})]})})]})},qe=Ne;export{qe as L};
