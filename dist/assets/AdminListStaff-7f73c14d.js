import{j as e}from"./@nivo/heatmap-ba1ecfff.js";import{r as i}from"./vendor-851db8c1.js";import{w as g,M as y,e as j}from"./index-12adfaa3.js";import{S as v}from"./react-select-c8303602.js";import{L as w}from"./ListStaff-697ba79c.js";import"./@craftjs/core-d3c11b68.js";import"./@fortawesome/react-fontawesome-13437837.js";import"./@fortawesome/fontawesome-svg-core-4fa3e289.js";import"./react-confirm-alert-cd7ccfe7.js";import"./@tanstack/react-query-20158223.js";import"./@stripe/stripe-js-6b714a86.js";import"./moment-a9aaa855.js";import"./cal-heatmap-cf010ec4.js";import"./react-icons-51bc3cff.js";import"./smoothscroll-polyfill-a5c0a116.js";import"./date-fns-07266b7d.js";import"./lodash-91d5d207.js";import"./numeral-ea653b2a.js";import"./@stripe/react-stripe-js-64f0e61f.js";import"./react-hook-form-687afde5.js";import"./@headlessui/react-a5400090.js";import"./@fortawesome/free-solid-svg-icons-0a9c4907.js";import"./@fortawesome/free-regular-svg-icons-0a88e957.js";import"./@fortawesome/free-brands-svg-icons-fae0dcac.js";import"./@mantine/core-8cbffb6d.js";import"./@emotion/react-89b506c3.js";import"./@emotion/cache-9a5b99cd.js";import"./@emotion/utils-8a8f62c5.js";import"./@emotion/serialize-460cad7f.js";import"./@uppy/dashboard-4a19149e.js";import"./@fullcalendar/core-8ccc1ac4.js";import"./@uppy/core-0760343f.js";import"./@uppy/aws-s3-c5961f7a.js";import"./@uppy/compressor-11f993e4.js";import"./index-be4468eb.js";import"./yup-54691517.js";import"./index.esm-9c6194ba.js";import"./AddButton.module-98aac587.js";import"./index-eb1bc208.js";import"./Skeleton-1e8bf077.js";import"./react-loading-skeleton-3d87d1f5.js";import"./TimeslotPicker-f994a694.js";import"./BottomDrawer-708783c0.js";import"./TimeSlotGrid-3140c36d.js";import"./InvitationCard-0d989a96.js";import"./RoleAccessManager-52edc173.js";import"./DataTable-a2248415.js";import"./HistoryComponent-90e771dd.js";let m=new y;function L(){const[s,p]=i.useState(null),[n,o]=i.useState(!1),[c,d]=i.useState([]),[N,f]=i.useState(null),[u,x]=i.useState([]);async function h(){o(!0);try{m.setTable("clubs");const t=await m.callRestAPI({},"GETALL");d(t.list)}catch(t){console.error("Error fetching data:",t)}finally{o(!1)}}const b=async t=>{o(!0);try{f({id:t.value,name:t.label,club_id:t.club_id}),await S(t.value)}catch(a){console.error("Error fetching data:",a)}finally{o(!1)}},S=async t=>{var a,l;o(!0);try{const r=await m.callRawAPI(`/v3/api/custom/courtmatchup/admin/profile/${t}`,{},"GET");p((a=r==null?void 0:r.model)==null?void 0:a.club),x((l=r==null?void 0:r.model)==null?void 0:l.sports)}catch(r){console.log(r)}finally{o(!1)}};return i.useEffect(()=>{h()},[]),e.jsxs("div",{className:"h-full bg-white p-4 sm:p-6 lg:p-8",children:[n&&e.jsx(j,{}),e.jsxs("div",{className:"mb-4 max-w-xl",children:[e.jsx("label",{className:"mb-2 block text-base font-medium text-gray-900",children:"Select club"}),e.jsx(v,{className:"w-full rounded-lg border border-gray-200 p-2.5 text-sm",options:c.map(t=>({value:t.user_id,label:t.name,club_id:t==null?void 0:t.id})),isMulti:!1,onChange:b})]}),s!=null&&s.id?e.jsx(w,{club:s,sports:u}):e.jsx("div",{className:"flex h-[calc(100vh-200px)] items-center justify-center",children:e.jsxs("div",{className:"text-center",children:[e.jsx("h3",{className:"mb-2 text-xl font-medium text-gray-900",children:"No Club Selected"}),e.jsx("p",{className:"text-gray-600",children:"Please select a club from the dropdown above to view and manage its details."})]})})]})}const yt=g(L,"staff","You don't have permission to view staff");export{yt as default};
