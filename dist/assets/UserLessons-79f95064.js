import{j as e}from"./@nivo/heatmap-ba1ecfff.js";import{f as Ee,r as s,b as Re,k as Ye,L as Fe}from"./vendor-851db8c1.js";import{G as je,A as He,u as ve,aq as Ve,b as V,am as ts,$ as fe,v as Y,d as Ae,aD as Ge,M as Ne,T as De,t as as,ar as Ue,E as qe,J as Je,c as Ze,ab as ns,e as Qe,aB as rs,aC as is}from"./index-12adfaa3.js";import{b as We,e as ls}from"./index.esm-b72032a7.js";import{B as Le}from"./BottomDrawer-708783c0.js";import{A as os}from"./AddPlayers-ff80a144.js";import{B as ze}from"./BackButton-11ba52b2.js";import{L as $e}from"./ReservationSummary-7084c977.js";import{g as Ke}from"./customThresholdUtils-f40b07d5.js";import{f as ue}from"./date-fns-07266b7d.js";import{T as Xe}from"./TimeSlots-4d6eb2b6.js";import{C as es}from"./Calendar-9031b5fe.js";import{S as ss}from"./SportTypeSelection-ee0cc3da.js";import{C as Pe}from"./CalendarIcon-b3488133.js";import{h as ds}from"./moment-a9aaa855.js";import"./@craftjs/core-d3c11b68.js";import"./@fortawesome/react-fontawesome-13437837.js";import"./@fortawesome/fontawesome-svg-core-4fa3e289.js";import"./react-confirm-alert-cd7ccfe7.js";import"./@tanstack/react-query-20158223.js";import"./@stripe/stripe-js-6b714a86.js";import"./cal-heatmap-cf010ec4.js";import"./react-icons-51bc3cff.js";import"./smoothscroll-polyfill-a5c0a116.js";import"./lodash-91d5d207.js";import"./numeral-ea653b2a.js";import"./@stripe/react-stripe-js-64f0e61f.js";import"./react-hook-form-687afde5.js";import"./react-select-c8303602.js";import"./@mantine/core-8cbffb6d.js";import"./@emotion/react-89b506c3.js";import"./@emotion/cache-9a5b99cd.js";import"./@emotion/utils-8a8f62c5.js";import"./@emotion/serialize-460cad7f.js";import"./@uppy/dashboard-4a19149e.js";import"./@fullcalendar/core-8ccc1ac4.js";import"./@uppy/core-0760343f.js";import"./@uppy/aws-s3-c5961f7a.js";import"./@uppy/compressor-11f993e4.js";import"./@headlessui/react-a5400090.js";import"./@fortawesome/free-solid-svg-icons-0a9c4907.js";import"./@fortawesome/free-regular-svg-icons-0a88e957.js";import"./@fortawesome/free-brands-svg-icons-fae0dcac.js";import"./index.esm-09a3a6b8.js";import"./react-tooltip-7a26650a.js";import"./ChevronLeftIcon-e5eecf9c.js";import"./ChevronRightIcon-efb4c46c.js";import"./SelectionOptionsCard-0d5c6ddd.js";import"./SelectionOption-01b973e9.js";let _e=new Ne,cs=new De;function ms({isOpen:c,onClose:d,selectedDate:i,players:$,sports:v,selectedSport:C,groups:B,coach:m,selectedTimes:x,selectedType:D,selectedSubType:u,userProfile:g}){var we,Ie;Ee();const[k,M]=s.useState(""),[t,T]=s.useState([]),[G,y]=s.useState(!1),[E,n]=s.useState(!1),[p,U]=s.useState(4),[ee,W]=s.useState(null),[h,F]=s.useState(1),[r,z]=s.useState([]),[S,Z]=s.useState(null),[oe,se]=s.useState(!1),[ce,K]=s.useState(null),{dispatch:te}=s.useContext(je);s.useContext(He);const[l,P]=s.useState(null),[O,A]=s.useState(null),[R,j]=s.useState(null),[o,L]=s.useState(null),[q,b]=s.useState(!1),[I,X]=s.useState({isOpen:!1,title:"",message:"",actionButtonText:"",actionButtonLink:"",type:"warning"}),{user_subscription:pe,user_permissions:ae,club:_}=ve(),he=localStorage.getItem("user"),me=x.reduce((f,N)=>{const ge=new Date(`2000/01/01 ${N.from}`),ye=(new Date(`2000/01/01 ${N.until}`)-ge)/(1e3*60*60);return f+ye},0),ne=Ke(_==null?void 0:_.custom_request_threshold,C,D,u,4,v),a=Ve({hourlyRate:m==null?void 0:m.hourly_rate,hours:me,playerCount:t.length,feeSettings:_==null?void 0:_.fee_settings});Re.useEffect(()=>{t.length>ne&&(console.log(`Clearing selected players: current ${t.length} exceeds new threshold ${ne}`),T([]),U(1),V(te,`Player selection cleared. New maximum is ${ne} players. Please select players again.`,4e3,"warning"))},[ne]);const J=()=>{U(f=>Math.min(f+1,ne))},re=()=>{U(f=>Math.max(f-1,0))};async function ie(){var f;try{se(!0);const{data:N,limit:ge,error:Q,message:ye}=await _e.getCustomerStripeCards();if(Q&&V(te,ye,5e3,"error"),!N)return;const le=(f=N==null?void 0:N.data)==null?void 0:f.find(be=>{var Be,Me;return be.id===((Me=(Be=N==null?void 0:N.data[0])==null?void 0:Be.customer)==null?void 0:Me.default_source)});K(le)}catch(N){console.error("ERROR",N),V(te,N.message,5e3,"error"),as(dispatch,N.code)}finally{se(!1)}}async function xe(){try{const f=await _e.callRawAPI("/v3/api/custom/courtmatchup/user/reservations/payment-intent/create",{amount:a.total},"POST");j(f.client_secret),L(f.payment_intent)}catch(f){console.error(f)}finally{b(!1)}}const Se=async()=>{if(!(pe!=null&&pe.planId)){X({isOpen:!0,title:"Subscription Required",message:"Please subscribe to a membership plan to book lessons with coaches",actionButtonText:"View Membership Plans",actionButtonLink:"/user/membership/buy",type:"warning"});return}if(!(ae!=null&&ae.allowCoach)){X({isOpen:!0,title:"Plan Upgrade Required",message:`Your current plan (${ae==null?void 0:ae.planName}) does not include coach lessons. Please upgrade your plan.`,actionButtonText:"Upgrade Plan",actionButtonLink:"/user/membership/buy",type:"error"});return}if(!t.length){X({isOpen:!0,title:"Players Required",message:"Please select at least one player",type:"warning"});return}const{duration:f,start_time:N,end_time:ge}=fe(x);y(!0);try{const Q=ue(new Date(i),"yyyy-MM-dd"),ye={sport_id:C,type:D,sub_type:u,date:Q,start_time:N,end_time:ge,duration:f,court_id:1,price:a.total,coach_fee:a.coachFee,service_fee:a.serviceFee,reservation_type:Ue.lesson,player_ids:t.map(be=>be.id),primary_player_id:(S==null?void 0:S.id)||(g==null?void 0:g.id),buddy_details:null,payment_status:0,payment_intent:null,coach_id:m==null?void 0:m.id},le=await _e.callRawAPI("/v3/api/custom/courtmatchup/user/reservations",ye,"POST");_e.setTable("activity_logs"),await _e.callRestAPI({user_id:he,activity_type:qe.lesson,action_type:Je.CREATE,data:JSON.stringify(ye),club_id:_==null?void 0:_.id,description:"Created a lesson reservation"},"POST"),le.error||(A(le.reservation_id),P(le.booking_id),await xe(),F(2))}catch(Q){console.error("ERROR",Q),X({isOpen:!0,title:"Reservation Error",message:Q.message||"Error creating lesson reservation",type:"error"})}finally{y(!1)}},Ce=async()=>{try{const f=await cs.getList("user",{filter:[`guardian,eq,${he}`,"role,cs,user"]});z(f.list)}catch(f){console.error("Error fetching family members:",f)}},w=f=>{const N=f.value||f;(N==null?void 0:N.id)!==(S==null?void 0:S.id)&&(Z(N),T(ge=>{const Q=ge.filter(le=>le.id!==(S==null?void 0:S.id));if(Q.some(le=>le.id===N.id)){const le=Q.filter(be=>be.id!==N.id);return[N,...le]}else return[N,...Q]}))},H=f=>{T(N=>N.some(Q=>Q.id===f.id)?N.filter(Q=>Q.id!==f.id):[...N,f])};Re.useEffect(()=>{ie(),Ce()},[]),Re.useEffect(()=>{g&&!S&&Z(g)},[g,S]);const de=_!=null&&_.lesson_description?JSON.parse(_==null?void 0:_.lesson_description):{reservation_description:"",payment_description:""};return e.jsxs(e.Fragment,{children:[e.jsx(ts,{isOpen:I.isOpen,onClose:()=>X({...I,isOpen:!1}),title:I.title,message:I.message,actionButtonText:I.actionButtonText,actionButtonLink:I.actionButtonLink,type:I.type}),e.jsx(Le,{isOpen:c,onClose:d,title:"Reservation detail",children:e.jsxs("div",{className:"mx-auto max-w-7xl space-y-6",children:[e.jsx(ze,{onBack:()=>{h===2?F(1):d()}}),h===1&&e.jsxs("div",{className:"mx-auto grid max-w-7xl grid-cols-1 gap-4 md:grid-cols-3",children:[e.jsx($e,{selectedSport:C,sports:v,selectedType:D,selectedSubType:u,selectedDate:i,selectedTimes:x,playersNeeded:p,selectedCoach:m,timeRange:fe(x)}),e.jsx("div",{className:"space-y-4",children:e.jsx(os,{searchQuery:k,setSearchQuery:M,selectedPlayers:t,setSelectedPlayers:T,onPlayerToggle:H,players:$,groups:B,selectedGroup:ee,isFindBuddyEnabled:E,setIsFindBuddyEnabled:n,playersNeeded:p,handleIncrement:J,handleDecrement:re,showPlayersNeeded:!1,showAddReservationToFindBuddy:!1,maximumPlayers:ne,familyMembers:r,currentUser:S,onCurrentUserChange:w,userProfile:g})}),e.jsxs("div",{className:"h-fit rounded-lg bg-white shadow-5",children:[e.jsx("div",{className:"rounded-lg bg-gray-50 p-4 text-center",children:e.jsx("h2",{className:"text-base font-medium",children:"Reserving details"})}),e.jsx("div",{className:"p-4",children:e.jsxs("div",{className:"space-y-2  ",children:[e.jsxs("div",{className:"divide-y",children:[e.jsxs("div",{className:"py-3",children:[e.jsxs("p",{className:"text-sm text-gray-500",children:["PLAYERS (",t.length,")"]}),e.jsx("div",{className:"mt-1",children:t.map(f=>e.jsxs("div",{className:"text-sm",children:[f.first_name," ",f.last_name]},f.id))})]}),e.jsxs("div",{className:"py-3",children:[e.jsx("p",{className:"text-sm text-gray-500",children:"COACH"}),e.jsx("div",{className:"mt-1",children:e.jsxs("div",{className:"text-sm",children:[(we=m==null?void 0:m.user)==null?void 0:we.first_name," ",(Ie=m==null?void 0:m.user)==null?void 0:Ie.last_name]})})]}),e.jsxs("div",{className:"py-3",children:[e.jsx("p",{className:"text-sm text-gray-500",children:"FEES"}),e.jsxs("div",{className:"mt-2 flex items-center justify-between",children:[e.jsxs("span",{className:"flex items-center gap-1",children:["Coach fee",e.jsxs("span",{className:"text-xs text-gray-500",children:["(",Y(m==null?void 0:m.hourly_rate),"/hr ×"," ",me,"hr × ",t.length," ","players)"]})]}),e.jsx("div",{className:"flex items-center gap-2",children:e.jsx("span",{children:Y(a.coachFee)})})]}),e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx("span",{children:"Service fee"}),e.jsx("div",{className:"flex items-center gap-2",children:e.jsx("span",{children:Y(a.serviceFee)})})]})]})]}),e.jsxs("div",{className:"flex items-center justify-between border-t pt-4",children:[e.jsx("span",{children:"Total"}),e.jsx("span",{className:"font-medium",children:Y(a.total)})]}),e.jsx("div",{className:"rounded-lg bg-[#F17B2C] p-3 text-sm text-white",children:e.jsxs("div",{className:"flex items-start gap-2",children:[e.jsx("svg",{width:"16",height:"16",viewBox:"0 0 16 16",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:e.jsx("path",{d:"M8.49364 2.62152L13.9236 12.1351C13.9737 12.2227 14 12.3221 14 12.4233C14 12.5245 13.9737 12.624 13.9236 12.7116C13.8736 12.7993 13.8017 12.8721 13.715 12.9227C13.6283 12.9733 13.5301 12.9999 13.43 12.9999H2.57C2.46995 12.9999 2.37165 12.9733 2.285 12.9227C2.19835 12.8721 2.12639 12.7993 2.07636 12.7116C2.02634 12.624 2 12.5245 2 12.4233C2 12.3221 2.02634 12.2227 2.07637 12.1351L7.50636 2.62152C7.5564 2.53387 7.62835 2.46109 7.715 2.41049C7.80165 2.35989 7.89995 2.33325 8 2.33325C8.10005 2.33325 8.19835 2.35989 8.285 2.41049C8.37165 2.46109 8.4436 2.53387 8.49364 2.62152ZM7.42998 10.117V11.2702H8.57002V10.117H7.42998ZM7.42998 6.08098V8.96387H8.57002V6.08098H7.42998Z",fill:"white"})}),e.jsx("span",{children:"After reserving, you will have 15 minutes to make the payment."})]})}),e.jsx(Ae,{loading:G,onClick:Se,className:"w-full rounded-lg bg-[#1E2841] py-3 text-white hover:bg-[#1E2841]/90",children:e.jsxs("div",{className:"flex flex-col items-center",children:[e.jsx("span",{children:"Reserve Now"}),e.jsx("span",{className:"text-sm opacity-80",children:"and continue to payment"})]})}),e.jsx("p",{className:"text-center text-sm text-gray-500",children:de.reservation_description}),e.jsx("p",{className:"text-center text-sm text-gray-500",children:"(You will not be charged yet)"})]})})]})]}),h===2&&e.jsx("div",{children:e.jsxs("div",{className:"mx-auto max-w-6xl",children:[e.jsx("div",{className:"rounded-xl bg-[#F17B2C] px-4 py-3 text-white",children:e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx("svg",{width:"24",height:"24",viewBox:"0 0 24 24",fill:"none",children:e.jsx("path",{d:"M12 8V12M12 16H12.01M21 12C21 16.9706 16.9706 21 12 21C7.02944 21 3 16.9706 3 12C3 7.02944 7.02944 3 12 3C16.9706 3 21 7.02944 21 12Z",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"})}),e.jsx("span",{children:"Your session is reserved. You have 15 minutes to complete the payment, otherwise the reservation will be canceled."})]})}),e.jsxs("div",{className:"mt-6 grid grid-cols-1 gap-6 lg:grid-cols-2",children:[e.jsx($e,{selectedSport:C,sports:v,selectedType:D,selectedSubType:u,selectedDate:i,selectedTimes:x,playersNeeded:p,timeRange:fe(x),selectedCoach:m}),e.jsx("div",{className:"space-y-6",children:e.jsxs("div",{className:"rounded-xl bg-white p-6 shadow-5",children:[e.jsx("h2",{className:"mb-4 text-center text-lg font-medium",children:"Payment details"}),e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("span",{className:"flex items-center gap-1",children:["Coach fee",e.jsxs("span",{className:"text-xs text-gray-500",children:["(",Y(m==null?void 0:m.hourly_rate),"/hr ×"," ",me,"hr × ",t.length," ","players)"]})]}),e.jsx("span",{children:Y(a.coachFee)})]}),e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx("span",{className:"text-gray-500",children:"Service fee"}),e.jsx("span",{children:Y(a.serviceFee)})]}),e.jsxs("div",{className:"flex items-center justify-between border-t pt-4",children:[e.jsx("span",{className:"font-medium",children:"Total"}),e.jsx("span",{className:"font-medium",children:Y(a.total)})]}),e.jsxs("div",{children:[e.jsx(Ge,{user:g,bookingId:l,reservationId:O,clientSecret:R,paymentIntent:o,navigateRoute:`/user/payment-success/${O}?type=lesson`}),e.jsx("p",{className:"text-sm text-gray-500",children:de.payment_description})]}),e.jsx("p",{className:"text-sm text-gray-500",children:"Lorem ipsum dolor sit amet, consectetur adipiscing elit. Curabitur erat nisi, porta a ipsum eu, accumsan dapibus enim. Donec ultrices congue libero in convallis. Cras condimentum felis eget dignissim tincidunt."})]})]})})]})]})})]})})]})}function xs({isOpen:c,onClose:d,coach:i,players:$,selectedLocation:v,selectedSport:C,sports:B,groups:m,club:x,selectedType:D,selectedSubType:u,userProfile:g,coachAvailability:k}){var l,P,O,A,R;const[M,t]=s.useState(new Date),[T,G]=s.useState(new Date),[y,E]=s.useState({from:null,until:null}),[n,p]=s.useState(!1),[U,ee]=s.useState([]),{dispatch:W}=s.useContext(je),{club_membership:h,user_subscription:F}=ve(),r=s.useMemo(()=>!(F!=null&&F.planId)||!(h!=null&&h.length)?null:h.find(j=>j.plan_id===F.planId),[F,h]),z=s.useMemo(()=>{var q,b;if(((q=r==null?void 0:r.advance_booking_enabled)==null?void 0:q.lesson)===!1){const I=new Date;return I.setFullYear(I.getFullYear()+10),I}const j=((b=r==null?void 0:r.advance_booking_days)==null?void 0:b.lesson)||10,o=new Date,L=new Date;return L.setDate(o.getDate()+j),L},[r]),S=j=>{if(!(i!=null&&i.availability)||!M)return!1;const o=ue(M,"EEEE").toLowerCase(),q=(Array.isArray(i.availability)?i.availability:Object.entries(i.availability).map(([I,X])=>({day:I,timeslots:X}))).find(I=>I.day===o);if(!q)return!1;const b=`${j.time24}:00`;return q.timeslots.includes(b)},Z=()=>{G(new Date(T.setMonth(T.getMonth()-1)))},oe=()=>{G(new Date(T.setMonth(T.getMonth()+1)))},se=()=>{var j,o;if(M>z&&((j=r==null?void 0:r.advance_booking_enabled)==null?void 0:j.lesson)!==!1){const L=((o=r==null?void 0:r.advance_booking_days)==null?void 0:o.lesson)||10;V(W,`Your membership plan only allows booking ${L} days in advance`,3e3,"warning");return}p(!0)},ce=()=>{p(!1)},K=j=>{console.log("Selected players:",j.selectedPlayers)},te=j=>{ee([{from:j.from,until:j.until}])};return e.jsxs(e.Fragment,{children:[e.jsx(Le,{isOpen:c&&!n,onClose:d,title:`${(l=i==null?void 0:i.user)==null?void 0:l.first_name}'s availability`,children:e.jsx("div",{className:"mx-auto max-w-3xl space-y-6 overflow-hidden",children:e.jsxs("div",{className:"grid grid-cols-2 gap-2",children:[e.jsx("div",{className:"h-fit rounded-lg bg-white p-4 shadow-5",children:e.jsxs("div",{children:[((P=r==null?void 0:r.advance_booking_enabled)==null?void 0:P.lesson)===!1?e.jsx("div",{className:"mb-2 rounded-lg bg-blue-50 p-2 text-xs text-blue-700",children:"You can reserve a lesson for any future date."}):e.jsxs("div",{className:"mb-2 rounded-lg bg-blue-50 p-2 text-xs text-blue-700",children:["Your ",(r==null?void 0:r.plan_name)||"current"," membership plan allows you to book lessons up to"," ",((O=r==null?void 0:r.advance_booking_days)==null?void 0:O.lesson)||10," ","days in advance."]}),e.jsx(es,{currentMonth:T,selectedDate:M,onDateSelect:j=>{var o,L;if(j>z&&((o=r==null?void 0:r.advance_booking_enabled)==null?void 0:o.lesson)!==!1){const q=((L=r==null?void 0:r.advance_booking_days)==null?void 0:L.lesson)||10;V(W,`Your membership plan only allows booking ${q} days in advance`,3e3,"warning");return}t(j)},onPreviousMonth:Z,onNextMonth:oe,daysOff:x!=null&&x.days_off?JSON.parse(x.days_off):[],coachAvailability:k,allowPastDates:!1,minDate:new Date,maxDate:z,disabledDateMessage:((A=r==null?void 0:r.advance_booking_enabled)==null?void 0:A.lesson)===!1?"You can book for any future date":`Your membership plan only allows booking ${((R=r==null?void 0:r.advance_booking_days)==null?void 0:R.lesson)||10} days in advance`})]})}),e.jsx(Xe,{selectedDate:M,onTimeClick:te,onNext:se,nextButtonText:"Next: Players",startHour:0,endHour:24,interval:30,isTimeSlotAvailable:S,timeRange:U,clubTimes:x!=null&&x.times?JSON.parse(x.times):[],coachAvailability:k,height:"h-full"})]})})}),e.jsx(ms,{players:$,isOpen:n,onClose:ce,selectedDate:M,timeRange:y,onNext:K,selectedSport:C,selectedLocation:v,sports:B,groups:m,coach:i,club:x,selectedTimes:U,selectedType:D,selectedSubType:u,userProfile:g})]})}function us({selectedCoach:c,onCheckAvailability:d,loadingAvailability:i}){var $,v,C,B,m;return c?e.jsx("div",{className:"h-fit rounded-lg bg-white p-4 shadow-5",children:e.jsxs("div",{className:"space-y-6",children:[e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx("h2",{className:"text-xl font-semibold",children:"Coach profile"}),e.jsx(Ae,{onClick:d,className:"rounded-xl bg-blue-900 px-3 py-2 text-sm text-white hover:bg-blue-800",loading:i,children:i?"Checking...":"Check availability"})]}),e.jsxs("div",{className:"flex items-center gap-4",children:[e.jsx("img",{src:c.photo||(($=c.user)==null?void 0:$.photo)||"/default-avatar.png",alt:`${(v=c.user)==null?void 0:v.first_name} ${(C=c.user)==null?void 0:C.last_name}`,className:"h-12 w-12 rounded-lg object-cover"}),e.jsxs("div",{children:[e.jsxs("h3",{className:"text-lg font-medium capitalize",children:[(B=c.user)==null?void 0:B.first_name," ",(m=c.user)==null?void 0:m.last_name]}),e.jsxs("p",{className:"text-sm text-gray-600",children:[Y(c.hourly_rate),"/h"]})]})]}),c.bio&&e.jsx("div",{className:"space-y-2",children:e.jsx("p",{className:"text-gray-600",children:c.bio})})]})}):null}let ps=new Ne;function hs({sports:c,coaches:d,players:i,groups:$,club:v,userProfile:C}){var se,ce,K,te;const[B,m]=s.useState("Tennis"),[x,D]=s.useState("Indoors"),[u,g]=s.useState(null),[k,M]=s.useState(""),[t,T]=s.useState(!0),[G,y]=s.useState(!1),[E,n]=s.useState(null),[p,U]=s.useState(null),[ee,W]=s.useState(!1),[h,F]=s.useState(null),[r]=Ye(),z=r.get("coach"),{user_permissions:S}=ve();s.useEffect(()=>{if(z){const l=d.find(P=>P.id===parseInt(z));g(l)}},[z,d]);const Z=c==null?void 0:c.find(l=>l.id===B),oe=async()=>{W(!0);try{const l=await ps.callRawAPI(`/v3/api/custom/courtmatchup/user/coach/availability/${u.id}`,{},"GET");F(l.availability),y(!0),console.log(l)}catch(l){console.log(l)}finally{W(!1)}};return e.jsxs(e.Fragment,{children:[e.jsx("div",{className:"space-y-6",children:e.jsx("div",{className:"mx-auto max-w-7xl p-4",children:e.jsxs("div",{className:"grid grid-cols-1 gap-8 md:grid-cols-3",children:[e.jsx(ss,{sports:c,userPermissions:S,onSelectionChange:({sport:l,type:P,subType:O})=>{m(l),n(P),U(O)}}),B&&(!((se=Z==null?void 0:Z.sport_types)!=null&&se.length)||E!==null&&(p!==null||!((te=(K=(ce=Z==null?void 0:Z.sport_types)==null?void 0:ce.find(l=>l.type===E))==null?void 0:K.subtype)!=null&&te.length)))?e.jsxs("div",{className:"max-h-fit space-y-6 rounded-xl bg-white p-4 shadow-5",children:[e.jsxs("div",{className:"relative",children:[e.jsx("input",{type:"text",placeholder:"Search by name",value:k,onChange:l=>M(l.target.value),className:"w-full rounded-lg border border-gray-300 py-2 pl-10 pr-4 focus:border-blue-500 focus:outline-none"}),e.jsx(Ze,{className:"absolute left-3 top-1/2 -translate-y-1/2 transform text-gray-400"}),e.jsx("button",{onClick:()=>T(!t),className:"absolute right-2 top-1/2 -translate-y-1/2 transform rounded-md border border-gray-300 px-2 py-1 text-sm hover:bg-gray-50",children:e.jsxs("div",{className:"flex items-center gap-1",children:[e.jsx("span",{children:"A-Z"}),e.jsx(We,{className:`text-xs transition-transform ${t?"":"rotate-180"}`})]})})]}),e.jsxs("div",{className:"max-h-[400px] space-y-4 overflow-y-auto",children:[d.length>0&&d.filter(l=>{var O,A;return`${(O=l.user)==null?void 0:O.first_name} ${(A=l.user)==null?void 0:A.last_name}`.toLowerCase().includes(k.toLowerCase())}).sort((l,P)=>{var R,j,o,L;const O=`${(R=l.user)==null?void 0:R.first_name} ${(j=l.user)==null?void 0:j.last_name}`.toLowerCase(),A=`${(o=P.user)==null?void 0:o.first_name} ${(L=P.user)==null?void 0:L.last_name}`.toLowerCase();return t?O.localeCompare(A):A.localeCompare(O)}).map(l=>{var P,O,A,R,j;return e.jsxs("div",{className:`flex cursor-pointer items-center justify-between rounded-lg border p-3 ${(u==null?void 0:u.id)===l.id?"border-primaryBlue bg-blue-50":"border-gray-100 hover:bg-gray-50"}`,onClick:()=>g(l),children:[e.jsxs("div",{className:"flex items-center gap-3",children:[e.jsx("img",{src:((P=l.user)==null?void 0:P.photo)||(l==null?void 0:l.photo)||"/default-avatar.png",alt:`${(O=l.user)==null?void 0:O.first_name} ${(A=l.user)==null?void 0:A.last_name}`,className:"h-10 w-10 rounded-full object-cover"}),e.jsx("div",{className:"flex flex-col",children:e.jsxs("span",{className:"font-medium capitalize",children:[(R=l.user)==null?void 0:R.first_name," ",(j=l.user)==null?void 0:j.last_name]})})]}),e.jsxs("span",{className:"text-gray-600",children:[Y(l.hourly_rate),"/h"]})]},l.id)}),d.length===0&&e.jsx("p",{className:"text-center text-sm text-gray-500",children:"No coaches found"})]})]}):e.jsx("div",{className:"col-span-2 flex h-full items-center justify-center rounded-lg bg-white p-8 shadow-5",children:e.jsxs("div",{className:"text-center text-gray-500",children:[e.jsx("svg",{className:"mx-auto h-12 w-12 text-gray-400",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor","aria-hidden":"true",children:e.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:"2",d:"M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"})}),e.jsx("h3",{className:"mt-2 text-sm font-medium text-gray-900",children:"Please select a sport"}),e.jsx("p",{className:"mt-1 text-sm text-gray-500",children:"Choose a sport, type, and sub-type to view available coaches"})]})}),e.jsx(us,{selectedCoach:u,onCheckAvailability:oe,loadingAvailability:ee})]})})}),e.jsx(xs,{isOpen:G,coachAvailability:h,onClose:()=>y(!1),selectedSport:B,selectedLocation:x,sports:c,groups:$,club:v,players:i,selectedType:E,selectedSubType:p,userProfile:C,coach:{...u,availability:JSON.parse((u==null?void 0:u.availability)||"{}")}})]})}let gs=new Ne;function ys({userProfile:c}){const[d,i]=s.useState(null),[$,v]=s.useState(!1),[C,B]=s.useState([]),{dispatch:m}=s.useContext(je),{dispatch:x}=s.useContext(He),[D,u]=s.useState([]),[g,k]=s.useState(!1),{sports:M}=ve();s.useEffect(()=>{const n=()=>{k(window.innerWidth<768)};return n(),window.addEventListener("resize",n),()=>window.removeEventListener("resize",n)},[]);async function t(){try{v(!0);const n=await gs.callRawAPI("/v3/api/custom/courtmatchup/user/reservations?custom_request=1",{},"GET");B(n.list)}catch(n){console.error(n)}finally{v(!1)}}s.useEffect(()=>{t()},[]),s.useEffect(()=>{C.length>0&&!d&&(i(C[0]),E(C[0]))},[C]);const T=n=>{switch(n){case 0:return"border-gray-300 border bg-gray-50 text-gray-600";case 1:return"border-green-300 border bg-green-50 text-green-600";case 2:return"border-red-300 border bg-red-50 text-red-600";default:return"border-gray-300 border bg-gray-50 text-gray-600"}},G=n=>{switch(n){case 0:return"Pending";case 1:return"Approved";case 2:return"Declined";default:return"Pending"}},y=(n,p,U)=>{const ee=M.find(h=>h.id===n);return`${ee?ee.name:"Unknown Sport"} • ${p} • ${U}`},E=async n=>{v(!0);try{const p=JSON.parse(n.player_ids),U=await ns(m,x,"user",p);i(n),u(U.list)}catch(p){console.error(p),V(m,p.message,5e3,"error")}finally{v(!1)}};return e.jsxs("div",{className:"relative flex h-full flex-col gap-3 p-2 md:flex-row",children:[$&&e.jsx(Qe,{}),e.jsx("div",{className:`${g?"w-full":"w-full md:w-1/3"} max-h-[300px] space-y-4 overflow-y-auto p-2 md:max-h-[600px]`,children:C.length>0?C.map(n=>e.jsxs("div",{onClick:()=>E(n),className:`cursor-pointer rounded-lg bg-white p-4 shadow-sm transition-shadow hover:shadow-md ${(d==null?void 0:d.reservation_id)===n.reservation_id?"ring-2 ring-blue-500":""}`,children:[e.jsx("div",{className:"mb-2 flex items-center justify-between",children:e.jsx("span",{className:`rounded-full px-3 py-1 text-xs font-medium ${T(n.booking_status)}`,children:G(n.booking_status)})}),e.jsxs("div",{className:"mb-2 flex items-center text-sm text-gray-600",children:[e.jsx("span",{className:"mr-1",children:e.jsx(Pe,{className:"h-4 w-4"})}),e.jsxs("div",{className:"text-xs md:text-sm",children:[ue(new Date(`${n.booking_date}T${n.start_time}`),"EEEE (MMM dd) • h:mmaaa")," ","-",ue(new Date(`${n.booking_date}T${n.end_time}`),"h:mmaaa")]})]}),e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx("div",{className:"text-xs font-medium md:text-sm",children:y(n.sport_id,n.sub_type,n.type)}),e.jsxs("div",{className:"flex items-center text-xs text-gray-600 md:text-sm",children:[e.jsx(ls,{className:"mr-1"}),e.jsx("span",{children:n.num_players})]})]})]},n.reservation_id)):!$&&e.jsxs("div",{className:"flex h-full w-full flex-col items-center justify-center rounded-lg bg-white p-6 text-center shadow-sm",children:[e.jsx("div",{className:"mb-4 text-gray-400",children:e.jsx(Pe,{className:"mx-auto h-12 w-12"})}),e.jsx("h3",{className:"mb-2 text-lg font-medium text-gray-900",children:"No Custom Requests"}),e.jsx("p",{className:"text-sm text-gray-600",children:"You don't have any custom requests at the moment."})]})}),d&&C.length>0&&e.jsxs("div",{className:`${g?"mt-4 w-full":"w-full md:w-2/3"} rounded-lg bg-white shadow-sm md:sticky md:top-20`,children:[e.jsx("div",{className:"rounded-xl bg-gray-100 p-4 text-center",children:e.jsx("p",{className:"text-sm md:text-base",children:"Custom request details"})}),e.jsxs("div",{className:"space-y-6 p-4",children:[e.jsxs("div",{className:"grid grid-cols-1 gap-2 rounded-xl bg-gray-100 p-2 text-xs sm:grid-cols-2 md:grid-cols-3",children:[e.jsxs("div",{children:[e.jsx("h3",{className:"mb-2 text-sm font-medium uppercase text-gray-500",children:"STATUS"}),e.jsx("span",{className:`rounded-full px-3 py-1 text-xs font-medium ${T(d.booking_status)}`,children:G(d.booking_status)})]}),e.jsxs("div",{children:[e.jsx("h3",{className:"mb-2 text-sm font-medium uppercase text-gray-500",children:"SPORT"}),e.jsx("p",{className:"text-xs md:text-sm",children:y(d.sport_id,d.sub_type,d.type)})]}),e.jsxs("div",{children:[e.jsx("h3",{className:"mb-2 text-sm font-medium uppercase text-gray-500",children:"REQUEST DATE"}),e.jsxs("p",{className:"text-xs md:text-sm",children:[ue(new Date(`${d.booking_date}T${d.start_time}`),"EEEE (MMM dd) • h:mmaaa")," ","-",ue(new Date(`${d.booking_date}T${d.end_time}`),"h:mmaaa")]})]})]}),e.jsxs("div",{children:[e.jsx("h3",{className:"mb-2 text-sm font-medium uppercase text-gray-500",children:"SUBMITTED ON"}),e.jsx("p",{className:"text-xs md:text-sm",children:ue(new Date(d.reservation_created_at),"MMMM dd, yyyy, (EEEE)")})]}),D.length>0&&e.jsxs("div",{children:[e.jsxs("h3",{className:"mb-2 text-sm font-medium uppercase text-gray-500",children:["PLAYERS (",D.length,")"]}),e.jsx("div",{className:"flex flex-col gap-2 rounded-lg border border-gray-300 bg-gray-100 p-3",children:D.map(n=>e.jsxs("div",{className:"rounded-full text-xs md:text-sm",children:[n.first_name," ",n.last_name]},n.id))})]}),d.notes&&e.jsxs("div",{children:[e.jsx("h3",{className:"mb-2 text-sm font-medium uppercase text-gray-500",children:"REQUEST"}),e.jsx("p",{className:"text-xs text-gray-700 md:text-sm",children:d.notes})]})]})]})]})}const ke=new Ne,fs=new De;function vs({coaches:c,onClose:d,selectedDate:i,selectedSport:$,sports:v,players:C,groups:B,isOpen:m,selectedType:x,selectedSubType:D,selectedTimes:u,userProfile:g}){const[k,M]=s.useState(!0),[t,T]=s.useState(null),[G,y]=s.useState(""),[E,n]=s.useState(1),[p,U]=s.useState([]),[ee,W]=s.useState(1);s.useState(!1),s.useState(null);const[h,F]=s.useState(!1),[r,z]=s.useState([]),[S,Z]=s.useState(null);Ee();const{duration:oe,end_time:se,start_time:ce}=fe(u),[K,te]=s.useState(null),[l,P]=s.useState(null),[O,A]=s.useState(null),{dispatch:R}=s.useContext(je),[j,o]=s.useState(null),{user_subscription:L,user_permissions:q,club:b}=ve(),I=localStorage.getItem("user");console.log("selectedTimes",u);const X=Ke(b==null?void 0:b.custom_request_threshold,$,x,D,4,v);React.useEffect(()=>{p.length>X&&(console.log(`Clearing selected players: current ${p.length} exceeds new threshold ${X}`),U([]),W(1),V(R,`Player selection cleared. New maximum is ${X} players. Please select players again.`,4e3,"warning"))},[X]);const pe=async()=>{try{const a=await fs.getList("user",{filter:[`guardian,eq,${I}`,"role,cs,user"]});z(a.list)}catch(a){console.error("Error fetching family members:",a)}};React.useEffect(()=>{g&&!S&&Z(g),pe()},[g,S]);const ae=u.reduce((a,J)=>{const re=new Date(`2000/01/01 ${J.from}`),xe=(new Date(`2000/01/01 ${J.until}`)-re)/(1e3*60*60);return a+xe},0),_=Ve({hourlyRate:t==null?void 0:t.hourly_rate,hours:ae,playerCount:p.length,feeSettings:b==null?void 0:b.fee_settings}),he=async()=>{if(!(L!=null&&L.planId)){V(R,"Please subscribe to a membership plan to book lessons with coaches",3e3,"error");return}if(!(q!=null&&q.allowCoach)){V(R,`Your current plan (${q==null?void 0:q.planName}) does not include coach lessons. Please upgrade your plan.`,3e3,"error");return}if(!$||!i||!u||!p){V(R,"Please select all required fields",3e3,"error");return}F(!0);try{const a=await ke.callRawAPI("/v3/api/custom/courtmatchup/user/reservations/payment-intent/create",{amount:_.total},"POST");if(a.error){V(R,a.message,3e3,"error"),F(!1);return}te(a.client_secret),P(a.payment_intent);const J=ds(i).format("YYYY-MM-DD"),re={sport_id:$,type:x,sub_type:D,date:J,player_ids:p.map(xe=>xe.id),primary_player_id:(S==null?void 0:S.id)||(g==null?void 0:g.id),start_time:ce,end_time:se,price:_.total,coach_fee:_.coachFee,service_fee:_.serviceFee,duration:oe,coach_id:t.id,payment_intent:a.payment_intent,reservation_type:Ue.lesson},ie=await ke.callRawAPI("/v3/api/custom/courtmatchup/user/reservations",re,"POST");ke.setTable("activity_logs"),await ke.callRestAPI({user_id:I,activity_type:qe.lesson,action_type:Je.CREATE,data:JSON.stringify(re),club_id:b==null?void 0:b.id,description:"Created a lesson reservation"},"POST"),ie.error||(V(R,"Reservation created successfully",3e3,"success"),o(ie.reservation_id),A(ie.booking_id),n(3))}catch(a){console.error(a),V(R,a.message||"Error creating reservation",3e3,"error")}finally{F(!1)}},me=()=>{t&&n(2)},ne=b!=null&&b.lesson_description?JSON.parse(b==null?void 0:b.lesson_description):{reservation_description:"",payment_description:""};return e.jsx(Le,{onClose:d,isOpen:m,title:E===1?"Select Coach":"Reservation detail",children:e.jsxs("div",{className:"relative mx-auto h-[90vh] w-full max-w-7xl overflow-y-auto rounded-lg ",children:[e.jsx(ze,{onBack:()=>{E===1?n(2):d()}}),E===1&&e.jsxs("div",{className:"mt-6 grid grid-cols-1 gap-6 lg:grid-cols-2",children:[e.jsxs("div",{className:"max-h-fit space-y-6 rounded-xl bg-white p-4 shadow-5",children:[e.jsxs("div",{className:"relative",children:[e.jsx("input",{type:"text",placeholder:"Search by name",value:G,onChange:a=>y(a.target.value),className:"w-full rounded-lg border border-gray-300 py-2 pl-10 pr-4 focus:border-blue-500 focus:outline-none"}),e.jsx(Ze,{className:"absolute left-3 top-1/2 -translate-y-1/2 transform text-gray-400"}),e.jsx("button",{onClick:()=>M(!k),className:"absolute right-2 top-1/2 -translate-y-1/2 transform rounded-md border border-gray-300 px-2 py-1 text-sm hover:bg-gray-50",children:e.jsxs("div",{className:"flex items-center gap-1",children:[e.jsx("span",{children:"A-Z"}),e.jsx(We,{className:`text-xs transition-transform ${k?"":"rotate-180"}`})]})})]}),e.jsxs("div",{className:"max-h-[calc(90vh-200px)] space-y-4 overflow-y-auto",children:[c.length>0&&c.filter(a=>`${a==null?void 0:a.first_name} ${a==null?void 0:a.last_name}`.toLowerCase().includes(G.toLowerCase())).sort((a,J)=>{const re=`${a==null?void 0:a.first_name} ${a==null?void 0:a.last_name}`.toLowerCase(),ie=`${J==null?void 0:J.first_name} ${J==null?void 0:J.last_name}`.toLowerCase();return k?re.localeCompare(ie):ie.localeCompare(re)}).map(a=>e.jsxs("div",{className:`flex cursor-pointer items-center justify-between rounded-lg border p-3 ${(t==null?void 0:t.id)===a.id?"border-primaryBlue bg-blue-50":"border-gray-100 hover:bg-gray-50"}`,onClick:()=>T(a),children:[e.jsxs("div",{className:"flex items-center gap-3",children:[e.jsx("img",{src:(a==null?void 0:a.photo)||(a==null?void 0:a.photo)||"/default-avatar.png",alt:`${a==null?void 0:a.first_name} ${a==null?void 0:a.last_name}`,className:"h-10 w-10 rounded-full object-cover"}),e.jsx("div",{className:"flex flex-col",children:e.jsxs("span",{className:"font-medium capitalize",children:[a==null?void 0:a.first_name," ",a==null?void 0:a.last_name]})})]}),e.jsxs("span",{className:"text-gray-600",children:[Y(a.hourly_rate),"/h"]})]},a.id)),c.length===0&&e.jsx("p",{className:"text-center text-sm text-gray-500",children:"No coaches found"})]})]}),e.jsx("div",{children:t&&e.jsxs("div",{className:"h-fit rounded-lg bg-white shadow-5",children:[e.jsxs("div",{className:"space-y-6 p-4",children:[e.jsx("div",{className:"flex items-center justify-between",children:e.jsx("h2",{className:"text-xl font-semibold",children:"Coach Profile"})}),e.jsxs("div",{className:"flex items-center gap-4",children:[e.jsx("img",{src:(t==null?void 0:t.photo)||(t==null?void 0:t.photo)||"/default-avatar.png",alt:`${t==null?void 0:t.first_name} ${t==null?void 0:t.last_name}`,className:"h-16 w-16 rounded-lg object-cover"}),e.jsxs("div",{children:[e.jsxs("h3",{className:"text-lg font-medium capitalize",children:[t==null?void 0:t.first_name," ",t==null?void 0:t.last_name]}),e.jsxs("p",{className:"text-lg text-gray-600",children:[Y(t==null?void 0:t.hourly_rate),"/h"]})]})]}),(t==null?void 0:t.bio)&&e.jsx("div",{className:"space-y-2",children:e.jsx("p",{className:"text-gray-600",children:t==null?void 0:t.bio})})]}),e.jsx("div",{className:"border-t p-3",children:e.jsxs("button",{onClick:me,className:"rounded-lg bg-blue-900 px-4 py-2 text-white hover:bg-blue-800",children:["Continue with ",t==null?void 0:t.first_name]})})]})})]}),E===2&&e.jsxs("div",{className:"mx-auto grid max-w-7xl grid-cols-1 gap-4 md:grid-cols-3",children:[e.jsx($e,{selectedSport:$,sports:v,selectedType:x,selectedSubType:D,selectedDate:i,timeRange:fe(u),playersNeeded:ee,selectedCoach:t}),e.jsxs("div",{className:"h-fit rounded-lg bg-white shadow-5",children:[e.jsx("div",{className:"rounded-lg bg-gray-50 p-4 text-center",children:e.jsx("h2",{className:"text-base font-medium",children:"Reserving Details"})}),e.jsx("div",{className:"p-4",children:e.jsxs("div",{className:"space-y-2",children:[e.jsxs("div",{className:"divide-y",children:[e.jsxs("div",{className:"py-3",children:[e.jsxs("p",{className:"text-sm text-gray-500",children:["PLAYERS (",p.length,")"]}),e.jsx("div",{className:"mt-1",children:p.map(a=>e.jsxs("div",{className:"text-sm",children:[a.first_name," ",a.last_name]},a.id))})]}),e.jsxs("div",{className:"py-3",children:[e.jsx("p",{className:"text-sm text-gray-500",children:"COACH"}),e.jsx("div",{className:"mt-1",children:e.jsxs("div",{className:"text-sm",children:[t==null?void 0:t.first_name," ",t==null?void 0:t.last_name]})})]}),e.jsxs("div",{className:"py-3",children:[e.jsx("p",{className:"text-sm text-gray-500",children:"FEES"}),e.jsxs("div",{className:"mt-2 flex items-center justify-between",children:[e.jsx("span",{className:"flex items-center gap-1",children:"Coach fee"}),e.jsx("span",{children:Y(_.coachFee)})]}),e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx("span",{children:"Service fee"}),e.jsx("span",{children:Y(_.serviceFee)})]})]})]}),e.jsxs("div",{className:"flex items-center justify-between border-t pt-4",children:[e.jsx("span",{children:"Total"}),e.jsx("span",{className:"font-medium",children:Y(_.total)})]}),e.jsx("div",{className:"rounded-lg bg-[#F17B2C] p-3 text-sm text-white",children:e.jsxs("div",{className:"flex items-start gap-2",children:[e.jsx("svg",{width:"16",height:"16",viewBox:"0 0 16 16",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:e.jsx("path",{d:"M8.49364 2.62152L13.9236 12.1351C13.9737 12.2227 14 12.3221 14 12.4233C14 12.5245 13.9737 12.624 13.9236 12.7116C13.8736 12.7993 13.8017 12.8721 13.715 12.9227C13.6283 12.9733 13.5301 12.9999 13.43 12.9999H2.57C2.46995 12.9999 2.37165 12.9733 2.285 12.9227C2.19835 12.8721 2.12639 12.7993 2.07636 12.7116C2.02634 12.624 2 12.5245 2 12.4233C2 12.3221 2.02634 12.2227 2.07637 12.1351L7.50636 2.62152C7.5564 2.53387 7.62835 2.46109 7.715 2.41049C7.80165 2.35989 7.89995 2.33325 8 2.33325C8.10005 2.33325 8.19835 2.35989 8.285 2.41049C8.37165 2.46109 8.4436 2.53387 8.49364 2.62152ZM7.42998 10.117V11.2702H8.57002V10.117H7.42998ZM7.42998 6.08098V8.96387H8.57002V6.08098H7.42998Z",fill:"white"})}),e.jsx("span",{children:"After reserving, you will have 15 minutes to make the payment."})]})}),e.jsx(Ae,{loading:h,onClick:he,className:"w-full rounded-lg bg-[#1E2841] py-3 text-white hover:bg-[#1E2841]/90",children:e.jsxs("div",{className:"flex flex-col items-center",children:[e.jsx("span",{children:"Reserve Now"}),e.jsx("span",{className:"text-sm opacity-80",children:"and continue to payment"})]})}),e.jsx("p",{className:"text-center text-sm text-gray-500",children:ne.reservation_description}),e.jsxs("div",{className:"space-y-2 text-center text-sm text-gray-500",children:[e.jsx("p",{children:"(You will not be charged yet)"}),e.jsxs("p",{children:["For any issues, please contact our support team at"," ",e.jsx("a",{href:"mailto:<EMAIL>",className:"font-medium underline",children:"<EMAIL>"})]})]})]})})]})]}),E===3&&e.jsxs("div",{className:"mx-auto max-w-6xl",children:[e.jsx("div",{className:"rounded-xl bg-[#F17B2C] px-4 py-3 text-white",children:e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx("svg",{width:"24",height:"24",viewBox:"0 0 24 24",fill:"none",children:e.jsx("path",{d:"M12 8V12M12 16H12.01M21 12C21 16.9706 16.9706 21 12 21C7.02944 21 3 16.9706 3 12C3 7.02944 7.02944 3 12 3C16.9706 3 21 7.02944 21 12Z",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"})}),e.jsx("span",{className:"!text-sm",children:"Your session is reserved. You have 15 minutes to complete the payment, otherwise the reservation will be canceled."})]})}),e.jsxs("div",{className:"mt-6 grid grid-cols-1 gap-6 lg:grid-cols-2",children:[e.jsx("div",{className:"space-y-6",children:e.jsx($e,{selectedSport:$,sports:v,selectedType:x,selectedSubType:D,selectedDate:i,timeRange:fe(u),selectedCoach:t})}),e.jsx("div",{className:"space-y-6",children:e.jsxs("div",{className:"rounded-xl bg-white p-6 shadow-5",children:[e.jsx("h2",{className:"mb-4 text-center text-lg font-medium",children:"Payment details"}),e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx("span",{className:"text-gray-500",children:"Coach fee"}),e.jsxs("span",{children:[Y(t==null?void 0:t.hourly_rate),"/hr ×"," ",ae,"hr × ",p.length," players"]}),e.jsx("span",{children:Y((t==null?void 0:t.hourly_rate)*ae*p.length)})]}),e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx("span",{className:"text-gray-500",children:"Service fee"}),e.jsx("span",{children:Y(rs(b==null?void 0:b.fee_settings,_.baseAmount))})]}),e.jsxs("div",{className:"flex items-center justify-between border-t pt-4",children:[e.jsx("span",{className:"font-medium",children:"Total"}),e.jsx("span",{className:"font-medium",children:Y(_.total)})]}),e.jsx("div",{children:e.jsx(Ge,{user:g,bookingId:O,reservationId:j,clientSecret:K,paymentIntent:l,navigateRoute:`/user/payment-success/${j}?type=lesson`})}),e.jsx("p",{className:"text-center text-sm text-gray-500",children:ne.payment_description}),e.jsxs("div",{className:"space-y-4 text-sm text-gray-500",children:[e.jsxs("p",{children:['By clicking "Pay now" you agree to our'," ",e.jsx(Fe,{to:"/terms-and-conditions",target:"_blank",className:"font-medium underline",children:"Terms and Conditions"})," ","and"," ",e.jsx(Fe,{to:"/privacy-policy",target:"_blank",className:"font-medium underline",children:"Privacy Policy"}),". All sales are final unless stated otherwise."]}),e.jsxs("p",{children:["For any issues, please contact our support team at"," ",e.jsx("a",{href:"mailto:<EMAIL>",className:"font-medium underline",children:"<EMAIL>"})]})]})]})]})})]})]})]})})}let Oe=new Ne;new De;function js({sports:c=[],players:d=[],club:i,userProfile:$}){var he,me,ne,a,J,re,ie,xe,Se,Ce;const[v,C]=s.useState(null),[B,m]=s.useState(null),[x,D]=s.useState(null),[u,g]=s.useState(new Date),[k,M]=s.useState([]),[t,T]=s.useState(null),[G,y]=s.useState(null),[E,n]=s.useState([]),[p,U]=s.useState(0),[ee,W]=s.useState(0),[h,F]=s.useState(!1),[r,z]=s.useState([]),[S,Z]=s.useState([]),[oe,se]=s.useState(!1),{state:ce,dispatch:K}=s.useContext(je),[te,l]=s.useState({from:null,until:null}),[P,O]=s.useState("main"),{club_membership:A,user_subscription:R,user_permissions:j}=ve(),o=s.useMemo(()=>!(R!=null&&R.planId)||!(A!=null&&A.length)?null:A.find(w=>w.plan_id===R.planId),[R,A]),L=s.useMemo(()=>{var we;const w=((we=o==null?void 0:o.advance_booking_days)==null?void 0:we.lesson)||10,H=new Date,de=new Date;return de.setDate(H.getDate()+w),de},[o]),{start_time:q,end_time:b}=fe(r),I=async()=>{try{const w=await Oe.callRawAPI("/v3/api/custom/courtmatchup/user/groups",{},"GET");n(w.groups)}catch(w){console.error(w)}};s.useEffect(()=>{I()},[]),s.useEffect(()=>{},[P]);const X=()=>{g(new Date(u.setMonth(u.getMonth()-1)))},pe=()=>{g(new Date(u.setMonth(u.getMonth()+1)))},ae=w=>{z([{from:w.from,until:w.until}])};s.useEffect(()=>{p&&(k!=null&&k.length)?W(p*(k==null?void 0:k.length)):W(p)},[p,k]);const _=async()=>{F(!0);const w={start_time:q,sport_id:v,end_time:b,date:ue(new Date(x),"yyyy-MM-dd")};try{const H=await Oe.callRawAPI("/v3/api/custom/courtmatchup/user/coach/search-time-slots",w,"POST");if(!H.error){if(console.log("response",H),H.list.length===0){V(K,"No coaches found for the selected time slot",4e3,"error");return}Z(H.list),se(!0)}}catch(H){console.error("ERROR",H),V(K,H.message,5e3)}finally{F(!1)}};return e.jsx("div",{className:"",children:e.jsxs("div",{className:"p-4",children:[e.jsx("div",{className:"",children:e.jsx("div",{className:"space-y-6",children:e.jsx("div",{className:"mx-auto max-w-7xl p-4",children:e.jsxs("div",{className:"grid grid-cols-1 gap-8 md:grid-cols-3",children:[e.jsx(ss,{sports:c,userPermissions:j,onSelectionChange:({sport:w,type:H,subType:de})=>{C(w),T(H),y(de),D(null),z([])}}),v&&(!((me=(he=c==null?void 0:c.find(w=>w.id===v))==null?void 0:he.sport_types)!=null&&me.length)||t!==null&&(G!==null||!((re=(J=(a=(ne=c==null?void 0:c.find(w=>w.id===v))==null?void 0:ne.sport_types)==null?void 0:a.find(w=>w.type===t))==null?void 0:J.subtype)!=null&&re.length)))?e.jsxs(e.Fragment,{children:[e.jsxs("div",{className:"h-fit rounded-lg bg-white p-4 shadow-5",children:[((ie=o==null?void 0:o.advance_booking_days)==null?void 0:ie.lesson)!==void 0&&e.jsxs("div",{className:"mb-2 rounded-lg bg-blue-50 p-2 text-xs text-blue-700",children:["You can reserve a lesson up to"," ",(xe=o==null?void 0:o.advance_booking_days)==null?void 0:xe.lesson," ",((Se=o==null?void 0:o.advance_booking_days)==null?void 0:Se.lesson)===1?"day":"days"," ","in advance."]}),e.jsx(es,{currentMonth:u,selectedDate:x,onDateSelect:w=>{var H;if(w>L){const de=((H=o==null?void 0:o.advance_booking_days)==null?void 0:H.lesson)||10;V(K,`Your membership plan only allows booking ${de} days in advance`,3e3,"warning");return}D(w)},onPreviousMonth:X,onNextMonth:pe,daysOff:i!=null&&i.days_off?JSON.parse(i.days_off):[],allowPastDates:!1,minDate:new Date,maxDate:L,disabledDateMessage:`Your membership plan only allows booking ${((Ce=o==null?void 0:o.advance_booking_days)==null?void 0:Ce.lesson)||10} days in advance`})]}),x&&e.jsx(Xe,{isLoading:h,selectedDate:x,timeRange:r,onTimeClick:ae,onNext:()=>{var w;if(!r.length){V(K,"Please select a time slot",3e3,"error");return}if(x>L){const H=((w=o==null?void 0:o.advance_booking_days)==null?void 0:w.lesson)||10;V(K,`Your membership plan only allows booking ${H} days in advance`,3e3,"warning");return}_()},nextButtonText:"Next: Select coach",startHour:0,endHour:24,clubTimes:i!=null&&i.times?JSON.parse(i.times):[],interval:30,isTimeSlotAvailable:()=>!0})]}):e.jsx("div",{className:"col-span-2 flex h-full items-center justify-center rounded-lg bg-white p-8 shadow-5",children:e.jsxs("div",{className:"text-center text-gray-500",children:[e.jsx("svg",{className:"mx-auto h-12 w-12 text-gray-400",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor","aria-hidden":"true",children:e.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:"2",d:"M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"})}),e.jsx("h3",{className:"mt-2 text-sm font-medium text-gray-900",children:"Please select a sport"}),e.jsx("p",{className:"mt-1 text-sm text-gray-500",children:"Choose a sport, type, and sub-type to view available time slots"})]})})]})})})}),oe&&S.length>0&&e.jsx(vs,{coaches:S,onClose:()=>se(!1),selectedDate:x,selectedSport:v,selectedLocation:B,timeRange:te,sports:c,players:d,groups:E,club:i,isOpen:oe,selectedTimes:r,selectedPlayers:k,selectedType:t,selectedSubType:G,userProfile:$})]})})}let Te=new De,Ns=new Ne;function yt(){const[c]=Ye(),d=c.get("coach"),[i,$]=s.useState("coach"),[v,C]=s.useState([]),[B,m]=s.useState([]),{dispatch:x}=s.useContext(je),[D,u]=s.useState(!0),[g,k]=s.useState([]),[M,t]=s.useState(null),T=Ee(),G=[{id:"coach",label:"Find by coach"},{id:"time",label:"Find by time"},{id:"custom",label:"Custom request"}],{club:y,sports:E,user_permissions:n}=ve(),p=localStorage.getItem("user"),U=async()=>{try{const h=await Te.getOne("user",p,{});t(h.model)}catch(h){console.error(h)}},ee=async()=>{const h=await Te.getList("coach",{join:["user|user_id"],filter:[`courtmatchup_coach.club_id,eq,${parseInt(y==null?void 0:y.id)}`]}),F=await Te.getList("user",{filter:["role,cs,user",`club_id,eq,${parseInt(y==null?void 0:y.id)}`]});C(h.list),m(F.list)},W=async()=>{try{const h=await Ns.callRawAPI("/v3/api/custom/courtmatchup/user/groups",{},"GET");k(h.groups)}catch(h){console.error(h)}};return s.useEffect(()=>{(async()=>y!=null&&y.id&&(u(!0),await U(),await ee(),await W(),u(!1)))()},[y==null?void 0:y.id]),Re.useEffect(()=>{x({type:"SETPATH",payload:{path:"lessons"}}),d&&$("coach")},[d]),n&&!n.allowCoach?e.jsx(is,{message:`Your current plan (${n==null?void 0:n.planName}) does not include coach lessons. Please upgrade your plan to access this feature.`}):e.jsxs(e.Fragment,{children:[e.jsxs("div",{className:"bg-white px-3 py-3 sm:px-4 sm:py-4",children:[D&&e.jsx(Qe,{}),e.jsx("h1",{className:"mb-4 text-xl font-semibold sm:mb-6 sm:text-2xl",children:"Lessons"}),e.jsxs("div",{className:"flex flex-col space-y-4 sm:flex-row sm:items-center sm:justify-between sm:space-y-0",children:[e.jsx("div",{className:"flex max-w-fit divide-x overflow-x-auto rounded-xl border text-xs sm:mb-0 sm:text-sm",children:G.map(h=>e.jsx("button",{onClick:()=>$(h.id),className:`whitespace-nowrap px-2 py-1.5 sm:px-3 sm:py-2 ${i===h.id?"bg-white-600 font-medium":"bg-gray-100 text-gray-600"}`,children:h.label},h.id))}),i==="custom"&&e.jsx("div",{className:"flex justify-end",children:e.jsx("button",{onClick:()=>T("/user/create-custom-request"),className:"rounded-lg bg-primaryBlue px-3 py-1.5 text-sm text-white transition-colors hover:bg-blue-600 sm:px-4 sm:py-2",children:"Create request"})})]})]}),e.jsxs("div",{className:"mx-auto max-w-7xl",children:[i==="coach"&&e.jsx(hs,{sports:E,coaches:v,players:B,groups:g,club:y,userProfile:M}),i==="time"&&e.jsx(js,{sports:E,coaches:v,players:B,groups:g,club:y,userProfile:M}),i==="custom"&&e.jsx(ys,{sports:E,coaches:v,players:B,groups:g,club:y,userProfile:M})]})]})}export{yt as default};
