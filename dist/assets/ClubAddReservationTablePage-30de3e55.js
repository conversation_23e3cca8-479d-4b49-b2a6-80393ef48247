import{j as t}from"./@nivo/heatmap-ba1ecfff.js";import{b as a,f as v}from"./vendor-851db8c1.js";import{u as I}from"./react-hook-form-687afde5.js";import{o as w}from"./yup-2824f222.js";import{c as _,a as l}from"./yup-54691517.js";import{G as A,A as E,d as N,M as R,b as B,t as D}from"./index-12adfaa3.js";import"./react-quill-73fb9518.js";/* empty css                   */import{M as p}from"./MkdInput-3424cacd.js";import"./index-02625b16.js";import"./@craftjs/core-d3c11b68.js";import"./@fortawesome/react-fontawesome-13437837.js";import"./@fortawesome/fontawesome-svg-core-4fa3e289.js";import"./@hookform/resolvers-67648cca.js";import"./react-confirm-alert-cd7ccfe7.js";import"./@tanstack/react-query-20158223.js";import"./@stripe/stripe-js-6b714a86.js";import"./moment-a9aaa855.js";import"./cal-heatmap-cf010ec4.js";import"./react-icons-51bc3cff.js";import"./smoothscroll-polyfill-a5c0a116.js";import"./date-fns-07266b7d.js";import"./lodash-91d5d207.js";import"./numeral-ea653b2a.js";import"./@stripe/react-stripe-js-64f0e61f.js";import"./react-select-c8303602.js";import"./@mantine/core-8cbffb6d.js";import"./@emotion/react-89b506c3.js";import"./@emotion/cache-9a5b99cd.js";import"./@emotion/utils-8a8f62c5.js";import"./@emotion/serialize-460cad7f.js";import"./@uppy/dashboard-4a19149e.js";import"./@fullcalendar/core-8ccc1ac4.js";import"./@uppy/core-0760343f.js";import"./@uppy/aws-s3-c5961f7a.js";import"./@uppy/compressor-11f993e4.js";import"./@headlessui/react-a5400090.js";import"./@fortawesome/free-solid-svg-icons-0a9c4907.js";import"./@fortawesome/free-regular-svg-icons-0a88e957.js";import"./@fortawesome/free-brands-svg-icons-fae0dcac.js";const je=({setSidebar:h})=>{const{dispatch:n}=a.useContext(A),y=_({booking_id:l(),buddy_id:l(),user_id:l(),status:l()}).required(),{dispatch:x}=a.useContext(E),[b,T]=a.useState({}),[f,u]=a.useState(!1),S=v(),{register:i,handleSubmit:j,setError:g,setValue:C,formState:{errors:m}}=I({resolver:w(y)});a.useState([]);const k=async s=>{let c=new R;u(!0);try{for(let r in b){let o=new FormData;o.append("file",b[r].file);let d=await c.uploadImage(o);s[r]=d.url}c.setTable("reservation");const e=await c.callRestAPI({booking_id:s.booking_id,buddy_id:s.buddy_id,user_id:s.user_id,status:s.status},"POST");if(!e.error)B(n,"Added"),S("/club/reservation"),h(!1),n({type:"REFRESH_DATA",payload:{refreshData:!0}});else if(e.validation){const r=Object.keys(e.validation);for(let o=0;o<r.length;o++){const d=r[o];g(d,{type:"manual",message:e.validation[d]})}}u(!1)}catch(e){u(!1),console.log("Error",e),g("booking_id",{type:"manual",message:e.message}),D(x,e.message)}};return a.useEffect(()=>{n({type:"SETPATH",payload:{path:"reservation"}})},[]),t.jsxs("div",{className:" shadow-md rounded  mx-auto p-5",children:[t.jsx("h4",{className:"text-2xl font-medium",children:"Add Reservation"}),t.jsxs("form",{className:" w-full max-w-lg",onSubmit:j(k),children:[t.jsx(p,{type:"number",page:"add",name:"booking_id",errors:m,label:"Booking Id",placeholder:"Booking Id",register:i,className:""}),t.jsx(p,{type:"number",page:"add",name:"buddy_id",errors:m,label:"Buddy Id",placeholder:"Buddy Id",register:i,className:""}),t.jsx(p,{type:"number",page:"add",name:"user_id",errors:m,label:"User Id",placeholder:"User Id",register:i,className:""}),t.jsx(p,{type:"number",page:"add",name:"status",errors:m,label:"Status",placeholder:"Status",register:i,className:""}),t.jsx(N,{type:"submit",loading:f,disabled:f,className:"bg-primaryBlue text-white font-bold py-2 px-4 rounded focus:outline-none focus:shadow-outline",children:"Submit"})]})]})};export{je as default};
