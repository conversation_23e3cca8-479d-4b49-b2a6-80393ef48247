import{j as e}from"./@nivo/heatmap-ba1ecfff.js";import{f as d,r as o}from"./vendor-851db8c1.js";import{u as h,G as g,M as x,T as y}from"./index-12adfaa3.js";import{A as C}from"./AddClinics-b3464d90.js";import"./@craftjs/core-d3c11b68.js";import"./@fortawesome/react-fontawesome-13437837.js";import"./@fortawesome/fontawesome-svg-core-4fa3e289.js";import"./react-confirm-alert-cd7ccfe7.js";import"./@tanstack/react-query-20158223.js";import"./@stripe/stripe-js-6b714a86.js";import"./moment-a9aaa855.js";import"./cal-heatmap-cf010ec4.js";import"./react-icons-51bc3cff.js";import"./smoothscroll-polyfill-a5c0a116.js";import"./date-fns-07266b7d.js";import"./lodash-91d5d207.js";import"./numeral-ea653b2a.js";import"./@stripe/react-stripe-js-64f0e61f.js";import"./react-hook-form-687afde5.js";import"./react-select-c8303602.js";import"./@mantine/core-8cbffb6d.js";import"./@emotion/react-89b506c3.js";import"./@emotion/cache-9a5b99cd.js";import"./@emotion/utils-8a8f62c5.js";import"./@emotion/serialize-460cad7f.js";import"./@uppy/dashboard-4a19149e.js";import"./@fullcalendar/core-8ccc1ac4.js";import"./@uppy/core-0760343f.js";import"./@uppy/aws-s3-c5961f7a.js";import"./@uppy/compressor-11f993e4.js";import"./@headlessui/react-a5400090.js";import"./@fortawesome/free-solid-svg-icons-0a9c4907.js";import"./@fortawesome/free-regular-svg-icons-0a88e957.js";import"./@fortawesome/free-brands-svg-icons-fae0dcac.js";import"./BackButton-11ba52b2.js";import"./Calendar-9031b5fe.js";import"./react-tooltip-7a26650a.js";import"./ChevronLeftIcon-e5eecf9c.js";import"./ChevronRightIcon-efb4c46c.js";import"./TimeSlots-4d6eb2b6.js";import"./SportTypeSelection-ee0cc3da.js";import"./SelectionOptionsCard-0d5c6ddd.js";import"./SelectionOption-01b973e9.js";let i=new x,E=new y;function ut(){d();const{club:t,sports:p}=h(),[a,m]=o.useState([]),[S,s]=o.useState(!1),[c,n]=o.useState([]),{state:T,dispatch:l}=o.useContext(g),u=async()=>{s(!0);try{i.setTable("user");const r=await i.callRestAPI({filter:["role,cs,user"]},"GETALL");m(r.list)}catch(r){return console.error("Error fetching users:",r),[]}finally{s(!1)}},f=async()=>{s(!0);try{const r=await E.getList("coach",{filter:[`${i._project_id}_coach.club_id,cs,${t==null?void 0:t.id}`],join:["user|user_id"]});n(r.list)}catch(r){return console.error("Error fetching coaches:",r),[]}finally{s(!1)}};return o.useEffect(()=>{t!=null&&t.id&&(u(),f())},[t==null?void 0:t.id]),o.useEffect(()=>{l({type:"SETPATH",payload:{path:"program-clinics"}})},[]),e.jsx(e.Fragment,{children:e.jsx(C,{users:a,coaches:c,role:"club",sports:p,club:t})})}export{ut as default};
