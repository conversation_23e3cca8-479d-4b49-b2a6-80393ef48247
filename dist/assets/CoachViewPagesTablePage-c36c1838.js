import{j as t}from"./@nivo/heatmap-ba1ecfff.js";import{b as e,j as c}from"./vendor-851db8c1.js";import"./yup-54691517.js";import{M as d,G as m,t as x}from"./index-12adfaa3.js";import{S as u}from"./index-02625b16.js";import"./@craftjs/core-d3c11b68.js";import"./@fortawesome/react-fontawesome-13437837.js";import"./@fortawesome/fontawesome-svg-core-4fa3e289.js";import"./react-confirm-alert-cd7ccfe7.js";import"./@tanstack/react-query-20158223.js";import"./@stripe/stripe-js-6b714a86.js";import"./moment-a9aaa855.js";import"./cal-heatmap-cf010ec4.js";import"./react-icons-51bc3cff.js";import"./smoothscroll-polyfill-a5c0a116.js";import"./date-fns-07266b7d.js";import"./lodash-91d5d207.js";import"./numeral-ea653b2a.js";import"./@stripe/react-stripe-js-64f0e61f.js";import"./react-hook-form-687afde5.js";import"./react-select-c8303602.js";import"./@mantine/core-8cbffb6d.js";import"./@emotion/react-89b506c3.js";import"./@emotion/cache-9a5b99cd.js";import"./@emotion/utils-8a8f62c5.js";import"./@emotion/serialize-460cad7f.js";import"./@uppy/dashboard-4a19149e.js";import"./@fullcalendar/core-8ccc1ac4.js";import"./@uppy/core-0760343f.js";import"./@uppy/aws-s3-c5961f7a.js";import"./@uppy/compressor-11f993e4.js";import"./@headlessui/react-a5400090.js";import"./@fortawesome/free-solid-svg-icons-0a9c4907.js";import"./@fortawesome/free-regular-svg-icons-0a88e957.js";import"./@fortawesome/free-brands-svg-icons-fae0dcac.js";let a=new d;const Y=()=>{e.useContext(m);const{dispatch:p}=e.useContext(m),[o,n]=e.useState({}),[l,i]=e.useState(!0),s=c();return e.useEffect(function(){(async function(){try{i(!0),a.setTable("pages");const r=await a.callRestAPI({id:Number(s==null?void 0:s.id),join:""},"GET");r.error||(n(r.model),i(!1))}catch(r){i(!1),console.log("error",r),x(p,r.message)}})()},[]),t.jsx("div",{className:" shadow-md rounded  mx-auto p-5",children:l?t.jsx(u,{}):t.jsxs(t.Fragment,{children:[t.jsx("h4",{className:"text-2xl font-medium",children:"View Pages"}),t.jsx("div",{className:"mb-4 mt-4",children:t.jsxs("div",{className:"flex mb-4",children:[t.jsx("div",{className:"flex-1",children:"Header"}),t.jsx("div",{className:"flex-1",children:o==null?void 0:o.header})]})})]})})};export{Y as default};
