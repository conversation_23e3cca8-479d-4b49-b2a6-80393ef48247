import{j as t}from"./@nivo/heatmap-ba1ecfff.js";import{r as p,b as i}from"./vendor-851db8c1.js";import{G as l}from"./index-12adfaa3.js";import"./moment-a9aaa855.js";import"./react-input-emoji-f9b52b09.js";import"./AdminTickets-f5b8b9d5.js";import{$ as r}from"./@headlessui/react-a5400090.js";import"./@craftjs/core-d3c11b68.js";import"./@fortawesome/react-fontawesome-13437837.js";import"./@fortawesome/fontawesome-svg-core-4fa3e289.js";import"./react-confirm-alert-cd7ccfe7.js";import"./@tanstack/react-query-20158223.js";import"./@stripe/stripe-js-6b714a86.js";import"./cal-heatmap-cf010ec4.js";import"./react-icons-51bc3cff.js";import"./smoothscroll-polyfill-a5c0a116.js";import"./date-fns-07266b7d.js";import"./lodash-91d5d207.js";import"./numeral-ea653b2a.js";import"./@stripe/react-stripe-js-64f0e61f.js";import"./react-hook-form-687afde5.js";import"./react-select-c8303602.js";import"./@mantine/core-8cbffb6d.js";import"./@emotion/react-89b506c3.js";import"./@emotion/cache-9a5b99cd.js";import"./@emotion/utils-8a8f62c5.js";import"./@emotion/serialize-460cad7f.js";import"./@uppy/dashboard-4a19149e.js";import"./@fullcalendar/core-8ccc1ac4.js";import"./@uppy/core-0760343f.js";import"./@uppy/aws-s3-c5961f7a.js";import"./@uppy/compressor-11f993e4.js";import"./@fortawesome/free-solid-svg-icons-0a9c4907.js";import"./@fortawesome/free-regular-svg-icons-0a88e957.js";import"./@fortawesome/free-brands-svg-icons-fae0dcac.js";import"./index-be4468eb.js";import"./yup-54691517.js";import"./AddButton.module-98aac587.js";import"./index-eb1bc208.js";import"./Skeleton-1e8bf077.js";import"./react-loading-skeleton-3d87d1f5.js";import"./FormattedPhoneNumber-40dd7178.js";function _(){const[o,m]=p.useState(0);p.useState("");const{dispatch:a}=i.useContext(l),[c,s]=p.useState("open");return i.useEffect(()=>{a({type:"SETPATH",payload:{path:"customer-support"}})},[]),i.useEffect(()=>{o===0?s("open"):o===1&&s("resolved")},[o]),t.jsx("div",{className:"flex h-screen w-full flex-col bg-gray-50",children:t.jsx("div",{className:"flex h-full flex-col gap-4 bg-white px-5 py-5",children:t.jsx(r.Group,{onChange:m,children:t.jsxs(r.List,{className:"flex space-x-4 border-b border-gray-200",children:[t.jsx(r,{className:({selected:e})=>`px-1 pb-2 ${e?"border-b-2 border-blue-600 text-blue-600":"text-gray-500"}`,children:"Open chats"}),t.jsx(r,{className:({selected:e})=>`px-1 pb-2 ${e?"border-b-2 border-blue-600 text-blue-600":"text-gray-500"}`,children:"Resolved chats"}),t.jsx(r,{className:({selected:e})=>`px-1 pb-2 ${e?"border-b-2 border-blue-600 text-blue-600":"text-gray-500"}`,children:"Open tickets"}),t.jsx(r,{className:({selected:e})=>`px-1 pb-2 ${e?"border-b-2 border-blue-600 text-blue-600":"text-gray-500"}`,children:"Resolved tickets"})]})})})})}export{_ as default};
