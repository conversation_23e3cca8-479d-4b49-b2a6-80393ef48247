import{j as p}from"./@nivo/heatmap-ba1ecfff.js";import{C as h}from"./CourtManagement-ce0e4c50.js";import{u as y,e as E,M as b}from"./index-12adfaa3.js";import{r as i}from"./vendor-851db8c1.js";import"./@craftjs/core-d3c11b68.js";import"./@fortawesome/react-fontawesome-13437837.js";import"./@fortawesome/fontawesome-svg-core-4fa3e289.js";import"./react-hook-form-687afde5.js";import"./react-select-c8303602.js";import"./@mantine/core-8cbffb6d.js";import"./@emotion/react-89b506c3.js";import"./@emotion/cache-9a5b99cd.js";import"./@emotion/utils-8a8f62c5.js";import"./@emotion/serialize-460cad7f.js";import"./@uppy/dashboard-4a19149e.js";import"./@fullcalendar/core-8ccc1ac4.js";import"./@uppy/core-0760343f.js";import"./@uppy/aws-s3-c5961f7a.js";import"./@uppy/compressor-11f993e4.js";import"./BottomDrawer-708783c0.js";import"./HistoryComponent-90e771dd.js";import"./date-fns-07266b7d.js";import"./PencilIcon-35185602.js";import"./TrashIcon-7d213648.js";import"./TimeSlotGrid-3140c36d.js";import"./@headlessui/react-a5400090.js";import"./react-confirm-alert-cd7ccfe7.js";import"./@tanstack/react-query-20158223.js";import"./@stripe/stripe-js-6b714a86.js";import"./moment-a9aaa855.js";import"./cal-heatmap-cf010ec4.js";import"./react-icons-51bc3cff.js";import"./smoothscroll-polyfill-a5c0a116.js";import"./lodash-91d5d207.js";import"./numeral-ea653b2a.js";import"./@stripe/react-stripe-js-64f0e61f.js";import"./@fortawesome/free-solid-svg-icons-0a9c4907.js";import"./@fortawesome/free-regular-svg-icons-0a88e957.js";import"./@fortawesome/free-brands-svg-icons-fae0dcac.js";let C=new b;function ct(){const[j,s]=i.useState(null),[u,m]=i.useState(!1),{courts:x,club:o,sports:f,fetchClubData:d}=y(),a=o!=null&&o.exceptions?JSON.parse(o.exceptions):[],[g,e]=i.useState(Array.isArray(a)?a:[]),S=async()=>{var c,n,l;m(!0);try{const t=await C.callRawAPI("/v3/api/custom/courtmatchup/club/profile",{},"GET");s(t==null?void 0:t.model);try{const r=(n=(c=t.model)==null?void 0:c.club)!=null&&n.exceptions?JSON.parse((l=t==null?void 0:t.model)==null?void 0:l.club.exceptions):[];e(Array.isArray(r)?r:[])}catch(r){console.error("Error parsing exceptions:",r),e([])}console.log("response",t)}catch(t){console.log(t)}finally{m(!1)}};return i.useEffect(()=>{S()},[]),p.jsxs("div",{className:"px-4 sm:px-6 lg:px-8",children:[u&&p.jsx(E,{}),p.jsx(h,{exceptions:g,fetchSettings:d,setExceptions:e,setProfileSettings:s,sports:f,courts:x,club:o,edit_api:"/v3/api/custom/courtmatchup/club/profile-edit"})]})}export{ct as default};
