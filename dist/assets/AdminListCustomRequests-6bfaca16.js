import{j as e}from"./@nivo/heatmap-ba1ecfff.js";import{b as s,f as J}from"./vendor-851db8c1.js";import{w as Q,M as U,G as W,A as X,c as C,t as v}from"./index-12adfaa3.js";import"./index-be4468eb.js";import{c as ee,a as d}from"./yup-54691517.js";import{u as te}from"./react-hook-form-687afde5.js";import{o as se}from"./yup-2824f222.js";import"./AddButton.module-98aac587.js";import{P as re}from"./index-eb1bc208.js";import"./lodash-91d5d207.js";import ae from"./Skeleton-1e8bf077.js";import"./@craftjs/core-d3c11b68.js";import"./@fortawesome/react-fontawesome-13437837.js";import"./@fortawesome/fontawesome-svg-core-4fa3e289.js";import"./react-confirm-alert-cd7ccfe7.js";import"./@tanstack/react-query-20158223.js";import"./@stripe/stripe-js-6b714a86.js";import"./moment-a9aaa855.js";import"./cal-heatmap-cf010ec4.js";import"./react-icons-51bc3cff.js";import"./smoothscroll-polyfill-a5c0a116.js";import"./date-fns-07266b7d.js";import"./numeral-ea653b2a.js";import"./@stripe/react-stripe-js-64f0e61f.js";import"./react-select-c8303602.js";import"./@mantine/core-8cbffb6d.js";import"./@emotion/react-89b506c3.js";import"./@emotion/cache-9a5b99cd.js";import"./@emotion/utils-8a8f62c5.js";import"./@emotion/serialize-460cad7f.js";import"./@uppy/dashboard-4a19149e.js";import"./@fullcalendar/core-8ccc1ac4.js";import"./@uppy/core-0760343f.js";import"./@uppy/aws-s3-c5961f7a.js";import"./@uppy/compressor-11f993e4.js";import"./@headlessui/react-a5400090.js";import"./@fortawesome/free-solid-svg-icons-0a9c4907.js";import"./@fortawesome/free-regular-svg-icons-0a88e957.js";import"./@fortawesome/free-brands-svg-icons-fae0dcac.js";import"./@hookform/resolvers-67648cca.js";import"./react-loading-skeleton-3d87d1f5.js";let p=new U;const L=[{header:"Date",accessor:"create_at"},{header:"Requested by",accessor:"num_players_needed"},{header:"Number of Players",accessor:"players"},{header:"Coach preferred",accessor:"user_id"},{header:"Email reply",accessor:"start_time"},{header:"Status",accessor:"status"},{header:"",accessor:"actions"}],oe=()=>{const{dispatch:N}=s.useContext(W),{dispatch:x}=s.useContext(X),[g,S]=s.useState([]),[a,f]=s.useState(10),[M,k]=s.useState(0),[ie,V]=s.useState(0),[c,Z]=s.useState(0),[H,P]=s.useState(!1),[A,E]=s.useState(!1),[ne,R]=s.useState(!1);s.useState(!1),s.useState([]),s.useState([]),s.useState("eq");const[m,u]=s.useState(!0),[_,le]=s.useState(!1),[T,ce]=s.useState(!1);s.useState();const w=J(),y=s.useRef(null),q=ee({id:d(),email:d(),role:d(),status:d()}),{register:D,handleSubmit:F,formState:{errors:de}}=te({resolver:se(q)});function B(){o(c-1,a)}function $(){o(c+1,a)}async function o(t,i,r={},n=[]){u(!(T||_));try{p.setTable("custom_requests");const l=await p.callRestAPI({payload:{...r},page:t,limit:i,filter:n},"PAGINATE");l&&u(!1);const{list:z,total:K,limit:Y,num_pages:j,page:h}=l;S(z),f(Y),k(j),Z(h),V(K),P(h>1),E(h+1<=j)}catch(l){u(!1),console.log("ERROR",l),v(x,l.message)}}const I=t=>{t.search?o(0,a,{},[`first_name,cs,${t.search}`,`last_name,cs,${t.search}`]):o(0,a)},G=t=>{t.target.value===""?o(0,a):o(0,a,{},[`status,cs,${t.target.value}`])};s.useEffect(()=>{N({type:"SETPATH",payload:{path:"custom-requests"}});const i=setTimeout(async()=>{await o(1,a)},700);return()=>{clearTimeout(i)}},[]);const b=t=>{y.current&&!y.current.contains(t.target)&&R(!1)};s.useEffect(()=>(document.addEventListener("mousedown",b),()=>{document.removeEventListener("mousedown",b)}),[]);const O=async t=>{if(window.confirm("Are you sure you want to delete this clinic?"))try{p.setTable("find_a_buddy"),await p.callRestAPI({id:t},"DELETE"),o(c,a)}catch(i){console.error("Error deleting clinic:",i),v(x,i.message)}};return e.jsxs("div",{className:"h-screen px-8",children:[e.jsxs("div",{className:"flex flex-col items-start justify-between gap-4 py-3 md:flex-row lg:items-center",children:[e.jsxs("form",{className:"relative flex w-full min-w-[300px] max-w-[300px] flex-1 items-center",onSubmit:F(I),children:[e.jsx("div",{className:"pointer-events-none absolute inset-y-0 left-0 flex items-center pl-3",children:e.jsx(C,{className:"text-gray-500"})}),e.jsx("input",{type:"text",className:"block w-full rounded-md border border-gray-200 py-2 pl-10 pr-12 text-sm placeholder:text-gray-500 focus:border-blue-500 focus:outline-none focus:ring-1 focus:ring-blue-500",placeholder:"search...",...D("search")}),e.jsx("button",{type:"submit",className:"absolute bottom-0 right-0 top-0 px-3 text-gray-500 hover:text-gray-700",children:e.jsx(C,{className:"h-5 w-5"})})]}),e.jsxs("div",{className:"flex flex-wrap items-center gap-4",children:[e.jsxs("select",{className:"rounded-md border border-gray-200 px-3 py-2 pr-8 text-sm text-gray-700",defaultValue:"All",onChange:G,children:[e.jsx("option",{value:"",children:"Status: All"}),e.jsx("option",{value:"0",children:"Inactive"}),e.jsx("option",{value:"1",children:"Active"})]}),e.jsxs("select",{className:"rounded-md border border-gray-200 px-3 py-2 pr-8 text-sm text-gray-700",defaultValue:"All",children:[e.jsx("option",{children:"Viewing: Royal Club"}),e.jsx("option",{children:"Viewing: Royal Club"}),e.jsx("option",{children:"Viewing: Royal Club"})]}),e.jsxs("button",{onClick:()=>w("/club/program-clinics/add"),className:"inline-flex items-center gap-2 rounded-xl border border-gray-200 bg-white px-4 py-2 text-sm font-semibold text-gray-500 hover:bg-gray-100",children:[e.jsxs("svg",{width:"20",height:"20",viewBox:"0 0 20 20",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:[e.jsx("path",{d:"M10 6.45703V9.9987L12.9167 12.9154",stroke:"#868C98","stroke-width":"1.5","stroke-linecap":"round","stroke-linejoin":"round"}),e.jsx("path",{d:"M2.29102 3.95703V7.29036H5.62435",stroke:"#868C98","stroke-width":"1.5","stroke-linecap":"round","stroke-linejoin":"round"}),e.jsx("path",{d:"M2.70898 12.5707C3.76873 15.5646 6.62818 17.7096 9.98935 17.7096C14.2528 17.7096 17.709 14.2585 17.709 10.0013C17.709 5.74411 14.2528 2.29297 9.98935 2.29297C6.79072 2.29297 4.04646 4.23552 2.87521 7.00362",stroke:"#868C98","stroke-width":"1.5","stroke-linecap":"round","stroke-linejoin":"round"})]}),"History"]}),e.jsxs("button",{onClick:()=>w("/club/program-clinics/add"),className:"inline-flex items-center gap-2 rounded-xl border border-gray-200 bg-white px-4 py-2 text-sm font-semibold text-gray-500 hover:bg-gray-100",children:[e.jsx("svg",{width:"20",height:"20",viewBox:"0 0 20 20",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:e.jsx("path",{d:"M2.70898 6.25L5.60038 3.35861C5.84445 3.11453 6.24018 3.11453 6.48426 3.35861L9.37565 6.25M10.6257 13.75L13.517 16.6414C13.7611 16.8855 14.1568 16.8855 14.4009 16.6414L17.2923 13.75M6.04232 4.16667L6.04232 16.875M13.959 3.125L13.959 16.0417",stroke:"#868C98","stroke-width":"1.5","stroke-linecap":"round","stroke-linejoin":"round"})}),"Sort by: time"]})]})]}),m?e.jsx(ae,{}):e.jsxs("div",{className:"overflow-x-auto rounded-lg border border-gray-200 bg-white",children:[e.jsxs("table",{className:"w-full min-w-[1000px] table-auto divide-y divide-gray-200",children:[e.jsx("thead",{children:e.jsx("tr",{children:L.map((t,i)=>e.jsx("th",{scope:"col",className:"whitespace-nowrap px-6 py-4 text-left text-sm font-medium text-gray-500",children:t.header},i))})}),e.jsx("tbody",{className:"divide-y-8 divide-gray-50",children:g.map((t,i)=>e.jsx("tr",{className:"hover:bg-gray-50",children:L.map((r,n)=>r.accessor==""?e.jsx("td",{className:"whitespace-nowrap px-6 py-4",children:e.jsxs("div",{className:"flex items-center  gap-3",children:[e.jsx("button",{className:"rounded-full p-2 hover:bg-gray-100",children:e.jsx("svg",{width:"20",height:"20",viewBox:"0 0 20 20",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:e.jsx("path",{d:"M7.87776 9.05723L17.5007 3.70443M7.47619 9.05227L3.20388 4.53065C2.70178 3.99926 3.07851 3.125 3.8096 3.125H17.0881C17.732 3.125 18.1326 3.82416 17.807 4.3797L10.9883 16.0142C10.6164 16.6488 9.66476 16.5255 9.46676 15.8172L7.67304 9.40025C7.63664 9.27 7.56907 9.15057 7.47619 9.05227Z",stroke:"#868C98","stroke-width":"1.5","stroke-linecap":"square","stroke-linejoin":"round"})})}),e.jsx("button",{className:"rounded-full p-2 hover:bg-gray-100",children:e.jsx("svg",{width:"20",height:"20",viewBox:"0 0 20 20",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:e.jsx("path",{d:"M11.041 5.20951L13.5768 2.67377C13.9022 2.34833 14.4298 2.34833 14.7553 2.67376L17.3268 5.24525C17.6522 5.57069 17.6522 6.09833 17.3268 6.42377L14.791 8.95951M11.041 5.20951L2.53509 13.7154C2.37881 13.8717 2.29102 14.0837 2.29102 14.3047V17.7095H5.69584C5.91685 17.7095 6.12881 17.6217 6.28509 17.4654L14.791 8.95951M11.041 5.20951L14.791 8.95951",stroke:"#868C98","stroke-width":"1.5","stroke-linecap":"round","stroke-linejoin":"round"})})}),e.jsx("button",{onClick:()=>O(t.id),className:"rounded-full p-2 hover:bg-gray-100",children:e.jsx("svg",{width:"20",height:"20",viewBox:"0 0 20 20",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:e.jsx("path",{d:"M4.74071 16.928L3.99227 16.9763L4.74071 16.928ZM15.258 16.928L16.0064 16.9763L16.0064 16.9763L15.258 16.928ZM2.29102 4.04102C1.8768 4.04102 1.54102 4.3768 1.54102 4.79102C1.54102 5.20523 1.8768 5.54102 2.29102 5.54102V4.04102ZM17.7077 5.54102C18.1219 5.54102 18.4577 5.20523 18.4577 4.79102C18.4577 4.3768 18.1219 4.04102 17.7077 4.04102V5.54102ZM8.87435 8.95768C8.87435 8.54347 8.53856 8.20768 8.12435 8.20768C7.71014 8.20768 7.37435 8.54347 7.37435 8.95768H8.87435ZM7.37435 13.541C7.37435 13.9552 7.71014 14.291 8.12435 14.291C8.53856 14.291 8.87435 13.9552 8.87435 13.541H7.37435ZM12.6243 8.95768C12.6243 8.54347 12.2886 8.20768 11.8743 8.20768C11.4601 8.20768 11.1243 8.54347 11.1243 8.95768H12.6243ZM11.1243 13.541C11.1243 13.9552 11.4601 14.291 11.8743 14.291C12.2886 14.291 12.6243 13.9552 12.6243 13.541H11.1243ZM12.5014 4.97796C12.6046 5.3791 13.0135 5.62059 13.4146 5.51734C13.8158 5.4141 14.0573 5.00521 13.954 4.60407L12.5014 4.97796ZM3.20924 4.8393L3.99227 16.9763L5.48916 16.8797L4.70613 4.74273L3.20924 4.8393ZM5.57232 18.4577H14.4264V16.9577H5.57232V18.4577ZM16.0064 16.9763L16.7895 4.8393L15.2926 4.74273L14.5095 16.8797L16.0064 16.9763ZM16.041 4.04102H3.95768V5.54102H16.041V4.04102ZM2.29102 5.54102H3.95768V4.04102H2.29102V5.54102ZM16.041 5.54102H17.7077V4.04102H16.041V5.54102ZM14.4264 18.4577C15.2613 18.4577 15.9527 17.8094 16.0064 16.9763L14.5095 16.8797C14.5067 16.9236 14.4703 16.9577 14.4264 16.9577V18.4577ZM3.99227 16.9763C4.04602 17.8094 4.73744 18.4577 5.57232 18.4577V16.9577C5.52838 16.9577 5.49199 16.9236 5.48916 16.8797L3.99227 16.9763ZM7.37435 8.95768V13.541H8.87435V8.95768H7.37435ZM11.1243 8.95768V13.541H12.6243V8.95768H11.1243ZM9.99937 3.04102C11.2021 3.04102 12.2145 3.86359 12.5014 4.97796L13.954 4.60407C13.5008 2.84306 11.9031 1.54102 9.99937 1.54102V3.04102ZM7.49738 4.97796C7.78419 3.86359 8.79666 3.04102 9.99937 3.04102V1.54102C8.09567 1.54102 6.49797 2.84306 6.04472 4.60407L7.49738 4.97796Z",fill:"#868C98"})})})]})},n):r.mapping&&r.accessor==="status"?e.jsx("td",{className:"inline-block whitespace-nowrap px-6 py-5 text-sm",children:t[r.accessor]===1?e.jsx("span",{className:"rounded-md bg-[#D1FAE5] px-3 py-1 text-[#065F46]",children:r.mapping[t[r.accessor]]}):e.jsx("span",{className:"rounded-md bg-[#F4F4F4] px-3 py-1 text-[#393939]",children:r.mapping[t[r.accessor]]})},n):r.mapping?e.jsx("td",{className:"whitespace-nowrap px-6 py-4",children:r.mapping[t[r.accessor]]},n):e.jsx("td",{className:"whitespace-nowrap px-6 py-4",children:t[r.accessor]},n))},i))})]}),m&&e.jsx("div",{className:"px-6 py-4",children:e.jsx("p",{className:"text-gray-500",children:"Loading..."})}),!m&&g.length===0&&e.jsx("div",{className:"w-full px-6 py-4 text-center",children:e.jsx("p",{className:"text-gray-500",children:"No data available"})})]}),e.jsx(re,{currentPage:c,pageCount:M,pageSize:a,canPreviousPage:H,canNextPage:A,updatePageSize:t=>{f(t),o(1,t)},previousPage:B,nextPage:$,gotoPage:t=>o(t,a)})]})},We=Q(oe,"custom_requests","You don't have permission to access custom requests management");export{We as default};
