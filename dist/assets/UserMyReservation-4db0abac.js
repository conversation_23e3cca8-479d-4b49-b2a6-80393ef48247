import{j as s}from"./@nivo/heatmap-ba1ecfff.js";import{f as H,r as u,b as i}from"./vendor-851db8c1.js";import{_ as V,aG as Q,Z as c,a3 as T,v as S,M as ds,G as is,e as Ns,b as Y,d as Os,A as os,R as Gs,ab as Ys,T as Us,u as Hs,i as Vs,X as zs,Y as js,t as qs}from"./index-12adfaa3.js";import{c as Zs,a as K}from"./yup-54691517.js";import{u as Ks}from"./react-hook-form-687afde5.js";import{o as Js}from"./yup-2824f222.js";import{P as Qs}from"./index-eb1bc208.js";import"./lodash-91d5d207.js";import Xs from"./Skeleton-1e8bf077.js";import{L as Ws}from"./LoadingOverlay-87926629.js";import{R as X,T as W,P as v,F as ss,C as es,G as bs,L as Cs}from"./ReservationStatus-5ced670f.js";import{h as U}from"./moment-a9aaa855.js";import{S as vs}from"./react-select-c8303602.js";import"./@craftjs/core-d3c11b68.js";import"./@fortawesome/react-fontawesome-13437837.js";import"./@fortawesome/fontawesome-svg-core-4fa3e289.js";import"./react-confirm-alert-cd7ccfe7.js";import"./@tanstack/react-query-20158223.js";import"./@stripe/stripe-js-6b714a86.js";import"./cal-heatmap-cf010ec4.js";import"./react-icons-51bc3cff.js";import"./smoothscroll-polyfill-a5c0a116.js";import"./date-fns-07266b7d.js";import"./numeral-ea653b2a.js";import"./@stripe/react-stripe-js-64f0e61f.js";import"./@headlessui/react-a5400090.js";import"./@fortawesome/free-solid-svg-icons-0a9c4907.js";import"./@fortawesome/free-regular-svg-icons-0a88e957.js";import"./@fortawesome/free-brands-svg-icons-fae0dcac.js";import"./@mantine/core-8cbffb6d.js";import"./@emotion/react-89b506c3.js";import"./@emotion/cache-9a5b99cd.js";import"./@emotion/utils-8a8f62c5.js";import"./@emotion/serialize-460cad7f.js";import"./@uppy/dashboard-4a19149e.js";import"./@fullcalendar/core-8ccc1ac4.js";import"./@uppy/core-0760343f.js";import"./@uppy/aws-s3-c5961f7a.js";import"./@uppy/compressor-11f993e4.js";import"./@hookform/resolvers-67648cca.js";import"./react-loading-skeleton-3d87d1f5.js";function se({reservation:e,club:k,clubSports:a,players:t,onCancelClick:m}){var b;const y=H(),x=V(e==null?void 0:e.reservation_updated_at),o=Q(e,a);return s.jsx("div",{children:s.jsxs("div",{className:"space-y-6",children:[s.jsx("div",{className:" px-5 pt-5",children:s.jsxs("div",{className:"flex w-full items-center justify-between gap-2 rounded-xl bg-gray-100 p-2 px-3",children:[s.jsx("div",{children:s.jsx("div",{className:"text-sm text-gray-500",children:"STATUS"})}),s.jsx("div",{className:"flex items-center gap-2",children:s.jsx("div",{children:(e==null?void 0:e.booking_status)===c.PENDING&&s.jsxs("div",{className:"flex items-center gap-2",children:[s.jsx(X,{}),s.jsx(W,{timeLeft:x})]})||(e==null?void 0:e.booking_status)===c.SUCCESS&&s.jsx(v,{})||(e==null?void 0:e.booking_status)===c.FAIL&&s.jsx(ss,{})||(e==null?void 0:e.booking_status)===c.CANCELLED&&s.jsx(es,{})})})]})}),s.jsxs("div",{className:"divide-y px-5 py-1",children:[s.jsxs("div",{className:"py-3",children:[s.jsx("div",{className:" py-1 ",children:s.jsx("p",{className:"text-sm text-gray-500",children:"SPORT"})}),(b=a==null?void 0:a.find(d=>d.id===(e==null?void 0:e.sport_id)))==null?void 0:b.name," ",(e==null?void 0:e.type)&&`• ${e==null?void 0:e.type}`," ",(e==null?void 0:e.sub_type)&&`• ${e==null?void 0:e.sub_type}`]}),s.jsxs("div",{className:"py-3",children:[s.jsx("div",{className:" py-1 ",children:s.jsx("p",{className:"text-sm text-gray-500",children:"DATE"})}),s.jsx("p",{className:"mt-1 font-medium",children:U(e==null?void 0:e.booking_date).format("MMM D, YYYY")})]}),s.jsxs("div",{className:"py-3",children:[s.jsx("div",{className:" py-1 ",children:s.jsx("p",{className:"text-sm text-gray-500",children:"TIME"})}),s.jsxs("p",{className:"mt-1 font-medium",children:[T(e==null?void 0:e.start_time)," -"," ",T(e==null?void 0:e.end_time)]})]}),s.jsxs("div",{className:"py-3",children:[s.jsx("div",{className:" py-1 ",children:s.jsx("p",{className:"text-sm text-gray-500",children:"PLAYERS"})}),s.jsx("div",{className:"flex flex-col gap-2",children:t.map(d=>s.jsxs("div",{className:"flex items-center gap-2",children:[s.jsx("img",{src:d.photo||"/default-avatar.png",alt:d.first_name,className:"h-8 w-8 rounded-full"}),s.jsx("p",{className:"font-medium capitalize",children:d.first_name||d.last_name?`${d.first_name} ${d.last_name}`:"Player"})]},d.user_id))})]})]}),s.jsx("div",{className:"px-5 py-3",children:s.jsxs("div",{className:"rounded-xl bg-gray-100 p-3",children:[s.jsx("p",{className:"text-sm text-gray-500",children:"FEES"}),s.jsxs("div",{className:"mt-2",children:[s.jsxs("div",{className:"mb-2 flex justify-between",children:[s.jsx("p",{className:"text-sm ",children:"Club fee"}),s.jsx("p",{className:"text-sm ",children:S(e==null?void 0:e.club_fee)})]}),s.jsxs("div",{className:"flex justify-between",children:[s.jsx("p",{className:"text-sm ",children:"Service fee"}),s.jsx("p",{className:"text-sm ",children:S(e==null?void 0:e.service_fee)})]}),s.jsx("div",{className:"my-2 border-t border-gray-300"}),s.jsxs("div",{className:"flex justify-between font-medium",children:[s.jsx("p",{children:"Total"}),s.jsx("p",{children:S((e==null?void 0:e.club_fee)+(e==null?void 0:e.service_fee))})]})]}),(e==null?void 0:e.booking_status)==c.PENDING&&s.jsx("div",{className:"mt-3 flex items-center justify-between",children:s.jsx("button",{onClick:()=>{y(`/user/reservation-payment/${e==null?void 0:e.reservation_id}?type=${e==null?void 0:e.booking_type}`)},disabled:x==="0min",className:`w-full rounded-lg border border-gray-300 px-2 py-2 text-sm text-white ${x==="0min"?"cursor-not-allowed bg-gray-400":"bg-primaryBlue"}`,children:x==="0min"?"Time Expired":"Pay now"})}),(e==null?void 0:e.booking_status)==c.SUCCESS&&s.jsxs("div",{className:"mt-3 flex items-center justify-between",children:[s.jsxs("div",{className:"flex items-center gap-1",children:[s.jsx("svg",{width:"24",height:"24",viewBox:"0 0 24 24",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:s.jsx("path",{d:"M2.75 9.75102V18.2471C2.75 18.7994 3.19772 19.2471 3.75 19.2471L20.2461 19.2471C20.7984 19.2471 21.2461 18.7994 21.2461 18.2471V9.75102M2.75 9.75102V5.75391C2.75 5.20162 3.19772 4.75391 3.75 4.75391H20.2451C20.7962 4.75391 21.2435 5.19985 21.2444 5.751C21.2467 7.08434 21.2461 8.41768 21.2461 9.75102M2.75 9.75102H21.2461M6.75 13.251H9.75",stroke:"#868C98","stroke-width":"1.5","stroke-linecap":"round","stroke-linejoin":"round"})}),s.jsx("p",{className:"text-sm font-medium",children:"Credit card • 0089"})]}),s.jsx("button",{onClick:()=>{y(`/user/payment-receipt/${e==null?void 0:e.reservation_id}`)},className:"rounded-md border border-gray-300 px-2 py-1 text-sm text-gray-500",children:"View receipt"})]}),o&&(e==null?void 0:e.booking_status)===c.SUCCESS&&(e==null?void 0:e.booking_status)!==c.CANCELLED&&s.jsx("div",{className:"mt-3",children:s.jsx("button",{onClick:m,className:"w-full rounded-lg border border-red-300 bg-red-50 px-2 py-2 text-sm text-red-600 hover:bg-red-100",children:"Cancel Reservation"})}),s.jsx("div",{className:"mt-3",children:s.jsx("p",{className:"text-xs text-gray-500",children:"Lorem ipsum dolor sit amet consectetur adipisicing elit. Beatae id illum labore excepturi veritatis facere ducimus architecto quasi incidunt earum eligendi exercitationem, ipsam voluptas amet quaerat? Alias inventore eum debitis!"})})]})})]})})}function ee({reservation:e,club:k,clubSports:a,players:t,coach:m,onCancelClick:y}){var d;const x=H(),o=V(e==null?void 0:e.reservation_updated_at),b=Q(e,a);return s.jsx("div",{children:s.jsxs("div",{className:"space-y-6",children:[s.jsx("div",{className:" px-5 pt-5",children:s.jsxs("div",{className:"flex w-full items-center justify-between gap-2 rounded-xl bg-gray-100 p-2 px-3",children:[s.jsx("div",{children:s.jsx("div",{className:"text-sm text-gray-500",children:"STATUS"})}),s.jsxs("div",{className:"flex items-center gap-2",children:[s.jsx("div",{children:(e==null?void 0:e.booking_status)===c.PENDING&&s.jsx(X,{})||(e==null?void 0:e.booking_status)===c.SUCCESS&&s.jsx(v,{})||(e==null?void 0:e.booking_status)===c.FAIL&&s.jsx(ss,{})||(e==null?void 0:e.booking_status)===c.CANCELLED&&s.jsx(es,{})}),s.jsx("div",{children:s.jsx(W,{timeLeft:o})})]})]})}),s.jsxs("div",{className:"divide-y px-5 py-1",children:[s.jsxs("div",{className:"py-3",children:[s.jsx("div",{className:" py-1 ",children:s.jsx("p",{className:"text-sm text-gray-500",children:"SPORT"})}),(d=a==null?void 0:a.find(p=>p.id===(e==null?void 0:e.sport_id)))==null?void 0:d.name," ",(e==null?void 0:e.type)&&`• ${e==null?void 0:e.type}`," ",(e==null?void 0:e.sub_type)&&`• ${e==null?void 0:e.sub_type}`]}),s.jsxs("div",{className:"py-3",children:[s.jsx("div",{className:" py-1 ",children:s.jsx("p",{className:"text-sm text-gray-500",children:"DATE"})}),s.jsx("p",{className:"mt-1 font-medium",children:U(e==null?void 0:e.booking_date).format("MMM D, YYYY")})]}),s.jsxs("div",{className:"py-3",children:[s.jsx("div",{className:" py-1 ",children:s.jsx("p",{className:"text-sm text-gray-500",children:"TIME"})}),s.jsxs("p",{className:"mt-1 font-medium",children:[T(e==null?void 0:e.start_time)," -"," ",T(e==null?void 0:e.end_time)]})]}),s.jsxs("div",{className:"py-3",children:[s.jsx("div",{className:" py-1 ",children:s.jsx("p",{className:"text-sm text-gray-500",children:"COACH"})}),s.jsx("div",{className:"flex flex-col gap-2",children:m&&s.jsxs("div",{className:"flex items-center gap-2",children:[s.jsx("img",{src:m.photo||"/default-avatar.png",alt:m.first_name,className:"h-8 w-8 rounded-full"}),s.jsx("p",{className:"font-medium capitalize",children:m.first_name||m.last_name?`${m.first_name} ${m.last_name}`:"Coach"})]},m.user_id)})]}),s.jsxs("div",{className:"py-3",children:[s.jsx("div",{className:" py-1 ",children:s.jsx("p",{className:"text-sm text-gray-500",children:"PLAYERS"})}),s.jsx("div",{className:"flex flex-col gap-2",children:t.map(p=>s.jsxs("div",{className:"flex items-center gap-2",children:[s.jsx("img",{src:p.photo||"/default-avatar.png",alt:p.first_name,className:"h-8 w-8 rounded-full"}),s.jsx("p",{className:"font-medium capitalize",children:p.first_name||p.last_name?`${p.first_name} ${p.last_name}`:"Player"})]},p.user_id))})]})]}),s.jsx("div",{className:"px-5 py-3",children:s.jsxs("div",{className:"rounded-xl bg-gray-100 p-3",children:[s.jsx("p",{className:"text-sm text-gray-500",children:"FEES"}),s.jsxs("div",{className:"mt-2",children:[s.jsxs("div",{className:"mb-2 flex justify-between",children:[s.jsx("p",{className:"text-sm ",children:"Club fee"}),s.jsx("p",{className:"text-sm ",children:S(k==null?void 0:k.club_fee)})]}),s.jsxs("div",{className:"flex justify-between",children:[s.jsx("p",{className:"text-sm ",children:"Service fee"}),s.jsx("p",{className:"text-sm ",children:"$12.50"})]}),s.jsx("div",{className:"my-2 border-t border-gray-300"}),s.jsxs("div",{className:"flex justify-between font-medium",children:[s.jsx("p",{children:"Total"}),s.jsx("p",{children:S((e==null?void 0:e.club_fee)+12.5)})]})]}),(e==null?void 0:e.booking_status)===c.SUCCESS&&s.jsxs("div",{className:"mt-3 flex items-center justify-between",children:[s.jsxs("div",{className:"flex items-center gap-1",children:[s.jsx("svg",{width:"24",height:"24",viewBox:"0 0 24 24",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:s.jsx("path",{d:"M2.75 9.75102V18.2471C2.75 18.7994 3.19772 19.2471 3.75 19.2471L20.2461 19.2471C20.7984 19.2471 21.2461 18.7994 21.2461 18.2471V9.75102M2.75 9.75102V5.75391C2.75 5.20162 3.19772 4.75391 3.75 4.75391H20.2451C20.7962 4.75391 21.2435 5.19985 21.2444 5.751C21.2467 7.08434 21.2461 8.41768 21.2461 9.75102M2.75 9.75102H21.2461M6.75 13.251H9.75",stroke:"#868C98","stroke-width":"1.5","stroke-linecap":"round","stroke-linejoin":"round"})}),s.jsx("p",{className:"text-sm font-medium",children:"Credit card • 0089"})]}),s.jsx("button",{onClick:()=>{x(`/user/payment-receipt/${e==null?void 0:e.reservation_id}`)},className:"rounded-md border border-gray-300 px-2 py-1 text-sm text-gray-500",children:"View receipt"})]}),b&&(e==null?void 0:e.booking_status)===c.SUCCESS&&(e==null?void 0:e.booking_status)!==c.CANCELLED&&s.jsx("div",{className:"mt-3",children:s.jsx("button",{onClick:y,className:"w-full rounded-lg border border-red-300 bg-red-50 px-2 py-2 text-sm text-red-600 hover:bg-red-100",children:"Cancel Reservation"})}),s.jsx("div",{className:"mt-3",children:s.jsx("p",{className:"text-xs text-gray-500",children:"Lorem ipsum dolor sit amet consectetur adipisicing elit. Beatae id illum labore excepturi veritatis facere ducimus architecto quasi incidunt earum eligendi exercitationem, ipsam voluptas amet quaerat? Alias inventore eum debitis!"})})]})})]})})}function le({reservation:e,club:k,clubSports:a,players:t,onCancelClick:m}){var b;const y=H(),x=V(e==null?void 0:e.reservation_updated_at),o=Q(e,a);return s.jsx("div",{children:s.jsxs("div",{className:"space-y-6",children:[s.jsx("div",{className:" px-5 pt-5",children:s.jsxs("div",{className:"flex w-full items-center justify-between gap-2 rounded-xl bg-gray-100 p-2 px-3",children:[s.jsx("div",{children:s.jsx("div",{className:"text-sm text-gray-500",children:"STATUS"})}),s.jsx("div",{className:"flex items-center gap-2",children:s.jsx("div",{children:(e==null?void 0:e.booking_status)===c.PENDING&&s.jsxs("div",{className:"flex items-center gap-2",children:[s.jsx(X,{}),s.jsx(W,{timeLeft:x})]})||(e==null?void 0:e.booking_status)===c.SUCCESS&&s.jsx(v,{})||(e==null?void 0:e.booking_status)===c.FAIL&&s.jsx(ss,{})||(e==null?void 0:e.booking_status)===c.CANCELLED&&s.jsx(es,{})})})]})}),s.jsxs("div",{className:"divide-y px-5 py-1",children:[s.jsxs("div",{className:"py-3",children:[s.jsx("div",{className:" py-1 ",children:s.jsx("p",{className:"text-sm text-gray-500",children:"SPORT"})}),(b=a==null?void 0:a.find(d=>d.id===(e==null?void 0:e.sport_id)))==null?void 0:b.name," ",(e==null?void 0:e.type)&&`• ${e==null?void 0:e.type}`," ",(e==null?void 0:e.sub_type)&&`• ${e==null?void 0:e.sub_type}`]}),s.jsxs("div",{className:"py-3",children:[s.jsx("div",{className:" py-1 ",children:s.jsx("p",{className:"text-sm text-gray-500",children:"DATE"})}),s.jsx("p",{className:"mt-1 font-medium",children:U(e==null?void 0:e.booking_date).format("MMM D, YYYY")})]}),s.jsxs("div",{className:"py-3",children:[s.jsx("div",{className:" py-1 ",children:s.jsx("p",{className:"text-sm text-gray-500",children:"TIME"})}),s.jsxs("p",{className:"mt-1 font-medium",children:[T(e==null?void 0:e.start_time)," -"," ",T(e==null?void 0:e.end_time)]})]}),s.jsxs("div",{className:"py-3",children:[s.jsx("div",{className:" py-1 ",children:s.jsx("p",{className:"text-sm text-gray-500",children:"PLAYERS"})}),s.jsx("div",{className:"flex flex-col gap-2",children:t==null?void 0:t.map(d=>s.jsxs("div",{className:"flex items-center gap-2",children:[s.jsx("img",{src:d.photo||"/default-avatar.png",alt:d.first_name,className:"h-8 w-8 rounded-full"}),s.jsx("p",{className:"font-medium capitalize",children:d.first_name||d.last_name?`${d.first_name} ${d.last_name}`:"Player"})]},d.user_id))})]})]}),s.jsx("div",{className:"px-5 py-3",children:s.jsxs("div",{className:"rounded-xl bg-gray-100 p-3",children:[s.jsx("p",{className:"text-sm text-gray-500",children:"FEES"}),s.jsxs("div",{className:"mt-2",children:[s.jsxs("div",{className:"mb-2 flex justify-between",children:[s.jsx("p",{className:"text-sm ",children:"Club fee"}),s.jsx("p",{className:"text-sm ",children:S(e==null?void 0:e.club_fee)})]}),s.jsxs("div",{className:"flex justify-between",children:[s.jsx("p",{className:"text-sm ",children:"Service fee"}),s.jsx("p",{className:"text-sm ",children:S(e==null?void 0:e.service_fee)})]}),s.jsx("div",{className:"my-2 border-t border-gray-300"}),s.jsxs("div",{className:"flex justify-between font-medium",children:[s.jsx("p",{children:"Total"}),s.jsx("p",{children:S((e==null?void 0:e.club_fee)+(e==null?void 0:e.service_fee))})]})]}),s.jsxs("div",{className:"mt-3 flex items-center justify-between",children:[s.jsxs("div",{className:"flex items-center gap-1",children:[s.jsx("svg",{width:"24",height:"24",viewBox:"0 0 24 24",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:s.jsx("path",{d:"M2.75 9.75102V18.2471C2.75 18.7994 3.19772 19.2471 3.75 19.2471L20.2461 19.2471C20.7984 19.2471 21.2461 18.7994 21.2461 18.2471V9.75102M2.75 9.75102V5.75391C2.75 5.20162 3.19772 4.75391 3.75 4.75391H20.2451C20.7962 4.75391 21.2435 5.19985 21.2444 5.751C21.2467 7.08434 21.2461 8.41768 21.2461 9.75102M2.75 9.75102H21.2461M6.75 13.251H9.75",stroke:"#868C98","stroke-width":"1.5","stroke-linecap":"round","stroke-linejoin":"round"})}),s.jsx("p",{className:"text-sm font-medium",children:"Credit card • 0089"})]}),s.jsx("button",{onClick:()=>{y(`/user/payment-receipt/${e==null?void 0:e.reservation_id}?type=clinic`)},className:"rounded-md border border-gray-300 px-2 py-1 text-sm text-gray-500",children:"View receipt"})]}),o&&(e==null?void 0:e.booking_status)==c.SUCCESS&&(e==null?void 0:e.booking_status)!=c.CANCELLED&&s.jsx("div",{className:"mt-3",children:s.jsx("button",{onClick:m,className:"w-full rounded-lg border border-red-300 bg-red-50 px-2 py-2 text-sm text-red-600 hover:bg-red-100",children:"Cancel Reservation"})}),s.jsx("div",{className:"mt-3",children:s.jsx("p",{className:"text-xs text-gray-500",children:"Lorem ipsum dolor sit amet consectetur adipisicing elit. Beatae id illum labore excepturi veritatis facere ducimus architecto quasi incidunt earum eligendi exercitationem, ipsam voluptas amet quaerat? Alias inventore eum debitis!"})})]})})]})})}const te=new ds;function ce({reservation:e,club:k,clubSports:a,players:t,onCancelClick:m}){var D;const y=H(),x=V(e==null?void 0:e.reservation_updated_at),{dispatch:o}=u.useContext(is),[b,d]=u.useState(!1),p=Q(e,a),z=async f=>{if(!f){Y(o,"Email is required",5e3,"error");return}try{d(!0);const M=await te.callRawAPI(`/v3/api/custom/courtmatchup/user/buddy/${e==null?void 0:e.buddy_id}/send-mail?email=${f}`,{},"GET");console.log(M),Y(o,"Email reminder sent",5e3,"success")}catch(M){console.log(M),Y(o,M==null?void 0:M.message,5e3,"error")}finally{d(!1)}};return s.jsxs("div",{children:[b&&s.jsx(Ns,{}),s.jsxs("div",{className:"space-y-6",children:[s.jsx("div",{className:" px-5 pt-5",children:s.jsxs("div",{className:"flex w-full items-center justify-between gap-2 rounded-xl bg-gray-100 p-2 px-3",children:[s.jsx("div",{children:s.jsx("div",{className:"text-sm text-gray-500",children:"STATUS"})}),s.jsx("div",{className:"flex items-center gap-2",children:s.jsxs("div",{children:[(e==null?void 0:e.num_needed)==(e==null?void 0:e.num_players)&&s.jsx(bs,{title:"Group full"}),(e==null?void 0:e.num_needed)!=(e==null?void 0:e.num_players)&&s.jsx(Cs,{numberOfBuddies:e==null?void 0:e.num_needed})]})})]})}),s.jsxs("div",{className:"divide-y px-5 py-1",children:[s.jsxs("div",{className:"py-3",children:[s.jsx("div",{className:" py-1 ",children:s.jsx("p",{className:"text-sm text-gray-500",children:"REQUEST MADE"})}),U(e==null?void 0:e.reservation_created_at).format("MMM D, YYYY")]}),s.jsxs("div",{className:"py-3",children:[s.jsx("div",{className:" py-1 ",children:s.jsx("p",{className:"text-sm text-gray-500",children:"DTE & TIME"})}),U(e==null?void 0:e.booking_date).format("MMM D, YYYY")," •"," ",T(e==null?void 0:e.start_time)," -"," ",T(e==null?void 0:e.end_time)]}),s.jsxs("div",{className:"py-3",children:[s.jsx("div",{className:" py-1 ",children:s.jsx("p",{className:"text-sm text-gray-500",children:"SPORT"})}),(D=a==null?void 0:a.find(f=>f.id===(e==null?void 0:e.sport_id)))==null?void 0:D.name," ",(e==null?void 0:e.type)&&`• ${e==null?void 0:e.type}`," ",(e==null?void 0:e.sub_type)&&`• ${e==null?void 0:e.sub_type}`]}),s.jsxs("div",{className:"py-3",children:[s.jsx("div",{className:" py-1 ",children:s.jsx("p",{className:"text-sm text-gray-500",children:"NOTES"})}),s.jsxs("p",{className:"mt-1 font-medium",children:[e==null?void 0:e.ntrp," ",(e==null?void 0:e.max_ntrp)&&`- ${e==null?void 0:e.max_ntrp}`]})]}),s.jsxs("div",{className:"py-3",children:[s.jsx("div",{className:" py-1 ",children:s.jsx("p",{className:"text-sm text-gray-500",children:"LOOKING FOR PLAYERS"})}),s.jsx("p",{className:"mt-1 font-medium",children:`${e==null?void 0:e.num_needed}/${e==null?void 0:e.num_players}`})]}),s.jsxs("div",{className:"py-3",children:[s.jsxs("div",{className:"flex items-center justify-between",children:[s.jsx("p",{className:"text-sm text-gray-500",children:"PLAYERS"}),s.jsx("button",{className:"rounded-lg border border-gray-300 bg-white px-2 py-1 text-sm text-gray-700",children:"Email all"})]}),s.jsx("div",{className:"mt-4 flex flex-col gap-4",children:t.map(f=>s.jsxs("div",{className:"flex items-center justify-between rounded-lg bg-gray-100 p-2",children:[s.jsxs("div",{className:"flex items-center gap-3",children:[s.jsx("img",{src:f.photo||"/default-avatar.png",alt:f.first_name,className:"h-10 w-10 rounded-full object-cover"}),s.jsxs("div",{children:[s.jsx("p",{className:"font-medium capitalize",children:f.first_name||f.last_name?`${f.first_name} ${f.last_name}`:"Player"}),s.jsx("p",{className:"text-sm text-gray-500",children:f.email})]})]}),s.jsxs("div",{className:"flex flex-col items-end gap-3",children:[s.jsx("button",{onClick:()=>z(f.email),className:"rounded-lg bg-white px-2 py-1 text-sm text-gray-700",children:"Send email"}),s.jsxs("p",{className:"text-sm text-gray-700",children:["NTRP: ",f.ntrp]})]})]},f.user_id))})]})]}),s.jsx("div",{className:"px-5 py-3",children:s.jsxs("div",{className:"rounded-xl bg-gray-100 p-3",children:[s.jsx("p",{className:"text-sm text-gray-500",children:"FEES"}),s.jsxs("div",{className:"mt-2",children:[s.jsxs("div",{className:"mb-2 flex justify-between",children:[s.jsx("p",{className:"text-sm ",children:"Club fee"}),s.jsx("p",{className:"text-sm ",children:S(e==null?void 0:e.club_fee)})]}),s.jsxs("div",{className:"flex justify-between",children:[s.jsx("p",{className:"text-sm ",children:"Service fee"}),s.jsx("p",{className:"text-sm ",children:S(e==null?void 0:e.service_fee)})]}),s.jsx("div",{className:"my-2 border-t border-gray-300"}),s.jsxs("div",{className:"flex justify-between font-medium",children:[s.jsx("p",{children:"Total"}),s.jsx("p",{children:S((e==null?void 0:e.club_fee)+(e==null?void 0:e.service_fee))})]})]}),(e==null?void 0:e.booking_status)==c.PENDING&&s.jsx("div",{className:"mt-3 flex items-center justify-between",children:s.jsx("button",{onClick:()=>{y(`/user/reservation-payment/${e==null?void 0:e.reservation_id}?type=${e==null?void 0:e.booking_type}`)},disabled:x==="0min",className:`w-full rounded-lg border border-gray-300 px-2 py-2 text-sm text-white ${x==="0min"?"cursor-not-allowed bg-gray-400":"bg-primaryBlue"}`,children:x==="0min"?"Time Expired":"Pay now"})}),(e==null?void 0:e.booking_status)==c.SUCCESS&&s.jsxs("div",{className:"mt-3 flex items-center justify-between",children:[s.jsxs("div",{className:"flex items-center gap-1",children:[s.jsx("svg",{width:"24",height:"24",viewBox:"0 0 24 24",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:s.jsx("path",{d:"M2.75 9.75102V18.2471C2.75 18.7994 3.19772 19.2471 3.75 19.2471L20.2461 19.2471C20.7984 19.2471 21.2461 18.7994 21.2461 18.2471V9.75102M2.75 9.75102V5.75391C2.75 5.20162 3.19772 4.75391 3.75 4.75391H20.2451C20.7962 4.75391 21.2435 5.19985 21.2444 5.751C21.2467 7.08434 21.2461 8.41768 21.2461 9.75102M2.75 9.75102H21.2461M6.75 13.251H9.75",stroke:"#868C98","stroke-width":"1.5","stroke-linecap":"round","stroke-linejoin":"round"})}),s.jsx("p",{className:"text-sm font-medium",children:"Credit card • 0089"})]}),s.jsx("button",{onClick:()=>{y(`/user/payment-receipt/${e==null?void 0:e.reservation_id}`)},className:"rounded-md border border-gray-300 px-2 py-1 text-sm text-gray-500",children:"View receipt"})]}),p&&(e==null?void 0:e.booking_status)===c.SUCCESS&&(e==null?void 0:e.booking_status)!==c.CANCELLED&&s.jsx("div",{className:"mt-3",children:s.jsx("button",{onClick:m,className:"w-full rounded-lg border border-red-300 bg-red-50 px-2 py-2 text-sm text-red-600 hover:bg-red-100",children:"Cancel Reservation"})}),s.jsx("div",{className:"mt-3",children:s.jsx("p",{className:"text-xs text-gray-500",children:"Lorem ipsum dolor sit amet consectetur adipisicing elit. Beatae id illum labore excepturi veritatis facere ducimus architecto quasi incidunt earum eligendi exercitationem, ipsam voluptas amet quaerat? Alias inventore eum debitis!"})})]})})]})]})}const ae=({isOpen:e,onClose:k,onCancel:a,loading:t=!1})=>s.jsxs("div",{className:`fixed inset-0 z-[999999] flex items-center justify-center ${e?"":"hidden"}`,children:[s.jsx("div",{className:"fixed inset-0 bg-black opacity-50"}),s.jsxs("div",{className:"relative z-50 w-full max-w-md rounded-3xl bg-white",children:[s.jsxs("div",{className:"p-6",children:[s.jsx("h2",{className:"mb-4 text-xl font-medium",children:"Cancel reservation"}),s.jsx("div",{className:"flex items-start justify-center rounded-xl bg-[#F17B2C] p-3",children:s.jsxs("div",{children:[s.jsx("div",{className:"text-sm font-medium text-white",children:"IMPORTANT"}),s.jsx("p",{className:"text-sm text-white",children:"Are you sure you want to cancel this reservation?"})]})})]}),s.jsxs("div",{className:"flex justify-end gap-3 border-t px-6 py-4",children:[s.jsx("button",{onClick:k,className:"rounded-xl border border-gray-300 px-4 py-2 hover:bg-gray-50",children:"Cancel"}),s.jsx(Os,{onClick:a,className:"rounded-xl bg-green-800 px-4 py-2 text-white hover:bg-green-700",loading:t,children:"Cancel reservation"})]})]})]});let J=new ds;function de({isOpen:e,onClose:k,clubSports:a,reservation:t,club:m,onReservationCanceled:y}){const{dispatch:x}=u.useContext(is),{dispatch:o}=u.useContext(os),[b,d]=u.useState(!1),[p,z]=u.useState([]),[D,f]=u.useState(null),[M,R]=u.useState(!1),[ms,q]=u.useState(!1);async function F(){try{const E=await Ys(x,o,"user",JSON.parse(t==null?void 0:t.player_ids),"user|user_id");z(E.list)}catch(E){console.error(E)}}async function B(){try{J.setTable("user");const E=await J.callRestAPI({id:t==null?void 0:t.coach_id},"GET");f(E.model)}catch(E){console.error(E)}}const ls=async()=>{q(!0);try{J.setTable("booking"),await J.callRestAPI({id:t==null?void 0:t.booking_id,status:c.CANCELLED},"PUT"),Y(x,"Reservation cancelled successfully",3e3,"success"),R(!1),t&&(t.booking_status=c.CANCELLED),y&&y()}catch(E){console.error("Error cancelling reservation:",E),Y(x,E.message||"Error cancelling reservation",3e3,"error")}finally{q(!1)}};return u.useEffect(()=>{(async()=>(d(!0),await F(),await B(),d(!1)))()},[t]),t?b?s.jsx(Ns,{}):(console.log({reservation:t,sports:a,players:p,coach:D}),s.jsxs(s.Fragment,{children:[s.jsxs(Gs,{isOpen:e,onClose:k,title:t.booking_type==="Court"?"Court details":t.booking_type==="Coach"?"Coach details":t.booking_type==="Clinic"?"Clinic details":"Details",showFooter:!1,className:"!p-0",children:[t.booking_type==="Court"&&s.jsx(se,{reservation:t,club:m,clubSports:a,players:p,onCancelClick:()=>R(!0)}),t.booking_type==="Find Buddy"&&s.jsx(ce,{reservation:t,club:m,clubSports:a,players:p,onCancelClick:()=>R(!0)}),t.booking_type==="Coach"&&s.jsx(ee,{reservation:t,club:m,clubSports:a,players:p,coach:D,onCancelClick:()=>R(!0)}),t.booking_type==="Clinic"&&s.jsx(le,{reservation:t,club:m,clubSports:a,players:p,onCancelClick:()=>R(!0)})]}),s.jsx(ae,{isOpen:M,onClose:()=>R(!1),onCancel:ls,loading:ms})]})):null}let A=new ds,as=new Us;const ys=[{header:"Date & Time",accessor:"date",isSorted:!1,isSortedDesc:!1,mappingExist:!1,mappings:{}},{header:"Booking Type",accessor:"booking_type",isSorted:!1,isSortedDesc:!1,mappingExist:!1,mappings:{}},{header:"Court",accessor:"court_id",isSorted:!1,isSortedDesc:!1,mappingExist:!1,mappings:{}},{header:"Players",accessor:"players",isSorted:!1,isSortedDesc:!1,mappingExist:!1},{header:"Price",accessor:"price",isSorted:!1,isSortedDesc:!1,mappingExist:!1,mappings:{}},{header:"Status",accessor:"reservation_status",isSorted:!1,isSortedDesc:!1,mappingExist:!0,mappings:{1:"Active",0:"Inactive"}}],Qe=()=>{const{dispatch:e,state:k}=i.useContext(is),{courts:a}=Hs(),{dispatch:t}=i.useContext(os),[m,y]=i.useState([]),[x,o]=i.useState(10),[b,d]=i.useState(0);i.useState(0);const[p,z]=i.useState(0),[D,f]=i.useState(!1),[M,R]=i.useState(!1),[ms,q]=i.useState(!1);i.useState(!1),i.useState([]),i.useState([]),i.useState("eq");const[F,B]=i.useState(!0),[ls,E]=i.useState(!1),[_s,ie]=i.useState(!1);i.useState(),H();const xs=i.useRef(null),[me,Ss]=i.useState(!1),[ns,ts]=i.useState(null);i.useState([]),u.useState(!1);const[ks,Es]=u.useState([]);u.useState([]),u.useState([]);const[r,rs]=u.useState("upcoming");u.useState(!1),u.useState(!1);const[Ls,Ms]=u.useState(null),[O,ws]=u.useState([]),[C,us]=u.useState(null),[ps,Ps]=u.useState(null),Ts=[{id:"upcoming",label:"Upcoming",icon:()=>s.jsx("svg",{width:"20",height:"20",viewBox:"0 0 20 20",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:s.jsx("path",{d:"M2.05153 8.29769L3.13684 14.4527C3.21675 14.906 3.64897 15.2086 4.10222 15.1287L8.42993 14.3656M2.05153 8.29769L1.61741 5.83567C1.53749 5.38242 1.84013 4.95021 2.29338 4.87029L11.7311 3.20616C12.1844 3.12624 12.6166 3.42888 12.6965 3.88213L13.1306 6.34414L2.05153 8.29769ZM13.3333 9.79243V11.6674L15 13.3341M18.5417 11.6674C18.5417 14.5439 16.2098 16.8758 13.3333 16.8758C10.4569 16.8758 8.125 14.5439 8.125 11.6674C8.125 8.79095 10.4569 6.4591 13.3333 6.4591C16.2098 6.4591 18.5417 8.79095 18.5417 11.6674Z",stroke:"black","stroke-width":"1.5","stroke-linecap":"round","stroke-linejoin":"round"})})},{id:"past",label:"Past",icon:()=>s.jsxs("svg",{width:"20",height:"20",viewBox:"0 0 20 20",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:[s.jsx("rect",{width:"20",height:"20",fill:"white"}),s.jsx("path",{d:"M12.5 7.91602L8.75001 12.4993L7.08334 10.8327M17.7083 9.99935C17.7083 14.2565 14.2572 17.7077 10 17.7077C5.74281 17.7077 2.29167 14.2565 2.29167 9.99935C2.29167 5.74215 5.74281 2.29102 10 2.29102C14.2572 2.29102 17.7083 5.74215 17.7083 9.99935Z",stroke:"#868C98","stroke-width":"1.5","stroke-linecap":"round","stroke-linejoin":"round"})]})},{id:"cancelled",label:"Cancelled",icon:()=>s.jsx("svg",{width:"20",height:"20",viewBox:"0 0 20 20",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:s.jsx("path",{d:"M12.5 7.49935L7.5 12.4993M12.5 12.4993L7.5 7.49935M17.7083 9.99935C17.7083 14.2565 14.2572 17.7077 10 17.7077C5.7428 17.7077 2.29166 14.2565 2.29166 9.99935C2.29166 5.74215 5.7428 2.29102 10 2.29102C14.2572 2.29102 17.7083 5.74215 17.7083 9.99935Z",stroke:"#868C98","stroke-width":"1.5","stroke-linecap":"round"})})}],Ds=Zs({id:K(),email:K(),role:K(),status:K()});Ks({resolver:Js(Ds)});function Rs(){w()}function As(){w()}async function w(l,_,G={},N=[]){B(!(_s||ls)),console.log("Selected family member:",C);try{let n;if(C&&C.value!=="all"&&C.value!=="me"?r==="past"||r==="cancelled"?n=await A.callRawAPI(`/v3/api/custom/courtmatchup/user/past-family-reservations/${C.value.id}`,{},"GET"):n=await A.callRawAPI(`/v3/api/custom/courtmatchup/user/family-reservations/${C.value.id}`,{},"GET"):r==="past"||r==="cancelled"?n=await A.callRawAPI("/v3/api/custom/courtmatchup/user/past-reservations",{},"GET"):n=await A.callRawAPI("/v3/api/custom/courtmatchup/user/reservations",{},"GET"),n){B(!1);const $=new Date;$.setHours(0,0,0,0);let g=n.list||n.reservations||[];if(console.log("Initial user reservations:",g.length),C&&C.value==="all"&&O.length>0)try{const h=localStorage.getItem("user"),L=O.filter(j=>j.id.toString()!==h);console.log(`Fetching reservations for ${L.length} family members`);const Z=L.map(j=>r==="past"||r==="cancelled"?A.callRawAPI(`/v3/api/custom/courtmatchup/user/past-family-reservations/${j.id}`,{},"GET"):A.callRawAPI(`/v3/api/custom/courtmatchup/user/family-reservations/${j.id}`,{},"GET"));(await Promise.all(Z)).forEach((j,I)=>{if(j&&(j.list||j.reservations)){const P=j.list||j.reservations||[];console.log(`Family member ${I+1} reservations:`,P.length),g=[...g,...P]}}),console.log("Total reservations before deduplication:",g.length),g=g.filter((j,I,P)=>I===P.findIndex(Bs=>Bs.reservation_id===j.reservation_id)),console.log("Combined reservations after deduplication:",g.length)}catch(h){console.error("Error fetching family reservations:",h)}r==="upcoming"?g=g.filter(h=>{const L=new Date(h.booking_date);return L.setHours(0,0,0,0),L>=$&&h.booking_status!==c.CANCELLED&&(h.booking_status===c.PENDING||h.booking_status===c.SUCCESS)}):r==="past"?g=g.filter(h=>{const L=new Date(h.booking_date);return L.setHours(0,0,0,0),L<$&&h.booking_status!==c.CANCELLED&&(h.booking_status===c.PENDING||h.booking_status===c.SUCCESS||h.booking_status===c.FAIL)}):r==="cancelled"&&(g=g.filter(h=>h.booking_status===c.CANCELLED)),y(g)}}catch(n){B(!1),console.log("ERROR",n),qs(t,n.message)}}const $s=async()=>{try{const l=localStorage.getItem("user"),_=await as.getOne("user",l,{}),G=await A.callRawAPI(`/v3/api/custom/courtmatchup/user/club/${_.model.club_id}`,{},"GET");Es(G.sports),Ms(G.model)}catch(l){console.error(l)}},Is=async()=>{try{const l=localStorage.getItem("user"),_=await as.getList("user",{filter:[`guardian,eq,${l}`,"role,cs,user"]});ws(_.list)}catch(l){console.error("Error fetching family members:",l)}},Fs=async()=>{try{const l=localStorage.getItem("user"),_=await as.getOne("user",l,{});Ps(_.model)}catch(l){console.error("Error fetching user profile:",l)}};i.useEffect(()=>{e({type:"SETPATH",payload:{path:"my-reservations"}});const _=setTimeout(async()=>{await w(1,x,{}),await $s(),await Is(),await Fs()},700);return()=>{clearTimeout(_)}},[]),i.useEffect(()=>{w(1,x,{})},[r]),i.useEffect(()=>{C!==null&&w(1,x,{})},[C]),i.useEffect(()=>{C===null&&us({value:"all",label:"All Reservations"})},[O,ps]);const hs=l=>{xs.current&&!xs.current.contains(l.target)&&q(!1)};i.useEffect(()=>(document.addEventListener("mousedown",hs),()=>{document.removeEventListener("mousedown",hs)}),[]);const fs=l=>{ts(l),Ss(!0)};console.log("reservation data",m);const gs=[{value:"all",label:"All Reservations"},{value:"me",label:"My Reservations"},...O.map(l=>({value:l,label:`${l.first_name} ${l.last_name} (${l.family_role||"Family Member"})`}))];return s.jsxs("div",{children:[s.jsxs("div",{className:"bg-white px-4 pt-4",children:[s.jsx("h1",{className:"mb-6 text-2xl font-semibold",children:"My Reservations"}),(O.length>0||ps)&&s.jsxs("div",{className:"mb-6 max-w-sm",children:[s.jsx("label",{className:"mb-2 block text-sm font-medium text-gray-900",children:"Filter by family member"}),s.jsx(vs,{className:"w-full text-sm",options:gs,onChange:us,value:C,placeholder:"Select family member",isSearchable:!1,defaultValue:gs[0]})]}),s.jsx("div",{className:"mb-0 flex max-w-fit  text-sm",children:Ts.map(l=>s.jsxs("button",{onClick:()=>rs(l.id),className:`flex items-center gap-2 bg-transparent px-3 py-3 ${r===l.id?"border-b-2 border-primaryBlue":""}`,children:[l.icon(),s.jsx("span",{className:"",children:l.label})]},l.id))})]}),s.jsxs("div",{className:"h-screen px-8",children:[F&&s.jsx(Ws,{}),F?s.jsx(Xs,{}):s.jsxs("div",{className:"overflow-x-auto",children:[s.jsxs("table",{className:"w-full min-w-[1024px] ",children:[s.jsx("thead",{children:s.jsx("tr",{children:ys.map((l,_)=>s.jsx("th",{scope:"col",className:"px-6 py-4 text-left text-sm font-medium text-gray-500",children:l.header},_))})}),s.jsx("tbody",{className:"divide-y-8 divide-gray-50",children:m.map((l,_)=>{const G=V(l==null?void 0:l.reservation_updated_at);return s.jsx("tr",{onClick:()=>fs(l),className:"hover:bg-gray-40 cursor-pointer rounded-lg bg-white px-4 py-3 text-gray-500",children:ys.map((N,n)=>{var $,g,h,L,Z,cs,j,I;return N.accessor==""?s.jsx("td",{className:"whitespace-nowrap px-6 py-4",children:s.jsx("div",{className:"flex items-center gap-3",children:s.jsx("button",{className:"rounded-full p-2 hover:bg-gray-100",onClick:()=>fs(l),children:s.jsx("svg",{width:"20",height:"20",viewBox:"0 0 20 20",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:s.jsx("path",{d:"M15.2083 12.7077V4.79102M15.2083 4.79102H7.29167M15.2083 4.79102L5 14.9993",stroke:"#868C98","stroke-width":"1.5","stroke-linecap":"round","stroke-linejoin":"round"})})})})},n):N.accessor==="reservation_status"?s.jsxs("td",{className:"flex gap-2 whitespace-nowrap px-6 py-5 text-sm",children:[(l==null?void 0:l.booking_type)=="Find Buddy"&&s.jsxs(s.Fragment,{children:[(l==null?void 0:l.num_needed)==(l==null?void 0:l.num_players)&&s.jsx(bs,{title:"Group full"}),(l==null?void 0:l.num_needed)!=(l==null?void 0:l.num_players)&&s.jsx(Cs,{numberOfBuddies:l==null?void 0:l.num_needed})]}),(l==null?void 0:l.booking_status)===c.PENDING&&s.jsxs("div",{className:"flex items-center gap-2",children:[s.jsx(X,{}),s.jsx(W,{timeLeft:G})]})||(l==null?void 0:l.booking_status)===c.SUCCESS&&s.jsx(v,{})||(l==null?void 0:l.booking_status)===c.FAIL&&s.jsx(ss,{}),(l==null?void 0:l.booking_status)===c.CANCELLED&&s.jsx(es,{})]},n):N.accessor==="court_id"?s.jsx("td",{className:"flex gap-2 whitespace-nowrap px-6 py-5 text-sm",children:(($=a.find(P=>P.id===(l==null?void 0:l.court_id)))==null?void 0:$.name)||"--"},n):N.mappingExist?s.jsx("td",{className:"whitespace-nowrap px-6 py-4",children:N.mappings[l[N.accessor]]},n):N.accessor==="type"?s.jsx("td",{className:"whitespace-nowrap px-6 py-4 capitalize",children:(g=Vs.find(P=>P.value===(l==null?void 0:l.type)))==null?void 0:g.label},n):N.accessor==="date"?s.jsxs("td",{className:"whitespace-nowrap rounded-l-3xl px-6 py-4",children:[zs((l==null?void 0:l.booking_date)||"")||"--"," "," | "," ",js((l==null?void 0:l.start_time)||"")||"--"," "," - "," ",js((l==null?void 0:l.end_time)||"")||"--"]},n):N.accessor==="booking_type"?s.jsx("td",{className:"whitespace-nowrap px-6 py-4 capitalize",children:(l==null?void 0:l.booking_type)||"--"},n):N.accessor==="players"?s.jsx("td",{className:"whitespace-nowrap px-6 py-4",children:(h=l==null?void 0:l.booking)!=null&&h.player_ids?`${JSON.parse((L=l==null?void 0:l.booking)==null?void 0:L.player_ids).length} players`:"0 players"},n):N.accessor==="price"?s.jsx("td",{className:"whitespace-nowrap px-6 py-4",children:S((l==null?void 0:l.price)||0)},n):N.accessor==="user"?s.jsx("td",{className:"whitespace-nowrap px-6 py-4",children:!((Z=l==null?void 0:l.user)!=null&&Z.first_name)||!((cs=l==null?void 0:l.user)!=null&&cs.last_name)?"--":`${(j=l==null?void 0:l.user)==null?void 0:j.first_name} ${(I=l==null?void 0:l.user)==null?void 0:I.last_name}`},n):s.jsx("td",{className:"whitespace-nowrap px-6 py-4 capitalize",children:l[N.accessor]},n)})},_)})})]}),!F&&m.length===0&&s.jsx("div",{className:"w-full px-6 py-4 text-center",children:s.jsx("p",{className:"text-gray-500",children:"No data available"})})]}),s.jsx(Qs,{currentPage:p,pageCount:b,pageSize:x,canPreviousPage:D,canNextPage:M,updatePageSize:l=>{o(l),w()},previousPage:Rs,nextPage:As,gotoPage:l=>w()}),s.jsx(de,{isOpen:!!ns,onClose:()=>ts(null),reservation:ns,clubSports:ks,club:Ls,onReservationCanceled:()=>{w(1,x,{}),ts(null)}})]})," "]})};export{Qe as default};
