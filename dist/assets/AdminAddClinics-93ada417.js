import{j as e}from"./@nivo/heatmap-ba1ecfff.js";import{f as b,r as t}from"./vendor-851db8c1.js";import"./BackButton-11ba52b2.js";import{G as L,e as C,M as E}from"./index-12adfaa3.js";import"./Calendar-9031b5fe.js";import{A as w}from"./AddClinics-b3464d90.js";import{C as j}from"./ClubSelection-5a94fb08.js";import"./@craftjs/core-d3c11b68.js";import"./@fortawesome/react-fontawesome-13437837.js";import"./@fortawesome/fontawesome-svg-core-4fa3e289.js";import"./react-confirm-alert-cd7ccfe7.js";import"./@tanstack/react-query-20158223.js";import"./@stripe/stripe-js-6b714a86.js";import"./moment-a9aaa855.js";import"./cal-heatmap-cf010ec4.js";import"./react-icons-51bc3cff.js";import"./smoothscroll-polyfill-a5c0a116.js";import"./date-fns-07266b7d.js";import"./lodash-91d5d207.js";import"./numeral-ea653b2a.js";import"./@stripe/react-stripe-js-64f0e61f.js";import"./react-hook-form-687afde5.js";import"./react-select-c8303602.js";import"./@mantine/core-8cbffb6d.js";import"./@emotion/react-89b506c3.js";import"./@emotion/cache-9a5b99cd.js";import"./@emotion/utils-8a8f62c5.js";import"./@emotion/serialize-460cad7f.js";import"./@uppy/dashboard-4a19149e.js";import"./@fullcalendar/core-8ccc1ac4.js";import"./@uppy/core-0760343f.js";import"./@uppy/aws-s3-c5961f7a.js";import"./@uppy/compressor-11f993e4.js";import"./@headlessui/react-a5400090.js";import"./@fortawesome/free-solid-svg-icons-0a9c4907.js";import"./@fortawesome/free-regular-svg-icons-0a88e957.js";import"./@fortawesome/free-brands-svg-icons-fae0dcac.js";import"./react-tooltip-7a26650a.js";import"./ChevronLeftIcon-e5eecf9c.js";import"./ChevronRightIcon-efb4c46c.js";import"./TimeSlots-4d6eb2b6.js";import"./SportTypeSelection-ee0cc3da.js";import"./SelectionOptionsCard-0d5c6ddd.js";import"./SelectionOption-01b973e9.js";let i=new E;function xt(){var n;b();const[u,f]=t.useState([]),[y,a]=t.useState(!1),[d,S]=t.useState([]),{state:T,dispatch:h}=t.useContext(L),[g,p]=t.useState(!0),[o,m]=t.useState(null),[x,l]=t.useState(!1),c=async()=>{a(!0);try{i.setTable("user");const s=await i.callRestAPI({filter:["role,cs,user"]},"GETALL");i.setTable("user");const r=await i.callRestAPI({filter:["role,cs,coach"]},"GETALL");f(s.list),S(r.list)}catch(s){return console.error("Error fetching users:",s),[]}finally{a(!1)}},A=async s=>{var r;console.log({club:s}),l(!0),m(s),await c((r=s.club)==null?void 0:r.id),p(!1),l(!1)};return t.useEffect(()=>{c()},[]),t.useEffect(()=>{h({type:"SETPATH",payload:{path:"program-clinics"}})},[]),console.log({selectedClub:o}),e.jsxs("div",{className:" p-8",children:[x&&e.jsx(C,{}),e.jsx(w,{sports:o==null?void 0:o.sports,users:u,coaches:d,role:"club",clubId:(n=o==null?void 0:o.club)==null?void 0:n.id}),g&&e.jsx(j,{setSelectedClub:m,selectedClub:o,setShow:p,onAction:A})]})}export{xt as default};
