import{j as t}from"./@nivo/heatmap-ba1ecfff.js";import{b as s,j as b,f}from"./vendor-851db8c1.js";import{u as w}from"./react-hook-form-687afde5.js";import{o as j}from"./yup-2824f222.js";import{c as y,a as N}from"./yup-54691517.js";import{A as v,G as S,M as A,b as k}from"./index-12adfaa3.js";import"./@craftjs/core-d3c11b68.js";import"./@fortawesome/react-fontawesome-13437837.js";import"./@fortawesome/fontawesome-svg-core-4fa3e289.js";import"./@hookform/resolvers-67648cca.js";import"./react-confirm-alert-cd7ccfe7.js";import"./@tanstack/react-query-20158223.js";import"./@stripe/stripe-js-6b714a86.js";import"./moment-a9aaa855.js";import"./cal-heatmap-cf010ec4.js";import"./react-icons-51bc3cff.js";import"./smoothscroll-polyfill-a5c0a116.js";import"./date-fns-07266b7d.js";import"./lodash-91d5d207.js";import"./numeral-ea653b2a.js";import"./@stripe/react-stripe-js-64f0e61f.js";import"./react-select-c8303602.js";import"./@mantine/core-8cbffb6d.js";import"./@emotion/react-89b506c3.js";import"./@emotion/cache-9a5b99cd.js";import"./@emotion/utils-8a8f62c5.js";import"./@emotion/serialize-460cad7f.js";import"./@uppy/dashboard-4a19149e.js";import"./@fullcalendar/core-8ccc1ac4.js";import"./@uppy/core-0760343f.js";import"./@uppy/aws-s3-c5961f7a.js";import"./@uppy/compressor-11f993e4.js";import"./@headlessui/react-a5400090.js";import"./@fortawesome/free-solid-svg-icons-0a9c4907.js";import"./@fortawesome/free-regular-svg-icons-0a88e957.js";import"./@fortawesome/free-brands-svg-icons-fae0dcac.js";const nt=()=>{var a,m;const l=y({email:N().email().required()}).required();s.useContext(v);const{dispatch:n}=s.useContext(S),[p,o]=s.useState(!1),r=b();f();const{register:c,handleSubmit:u,setError:d,formState:{errors:i}}=w({resolver:j(l)}),x=async g=>{let h=new A;try{o(!0);const e=await h.magicLoginAttempt(g.email,r==null?void 0:r.role);e.error||(o(!1),console.log(e),k(n,"Please check your mail to complete login attempt"))}catch(e){o(!1),console.log("Error",e),d("email",{type:"manual",message:e.message})}};return t.jsxs("div",{className:"w-full max-w-xs mx-auto",children:[t.jsxs("form",{onSubmit:u(x),className:"bg-white shadow-md rounded px-8 pt-6 pb-8 mb-4 mt-8 ",children:[t.jsxs("div",{className:"mb-4",children:[t.jsx("label",{className:"block text-gray-700 text-sm font-bold mb-2",htmlFor:"email",children:"Email"}),t.jsx("input",{type:"email",placeholder:"Email",...c("email"),className:`shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline ${(a=i.email)!=null&&a.message?"border-red-500":""}`}),t.jsx("p",{className:"text-red-500 text-xs italic",children:(m=i.email)==null?void 0:m.message})]}),t.jsx("div",{className:"flex items-center justify-between",children:t.jsxs("button",{type:"submit",className:"bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded focus:outline-none focus:shadow-outline",children:[p?"Attempting Log In...":"Sign In"," "]})})]}),t.jsxs("p",{className:"text-center text-gray-500 text-xs",children:["© ",new Date().getFullYear()," manaknightdigital inc. All rights reserved."]})]})};export{nt as default};
