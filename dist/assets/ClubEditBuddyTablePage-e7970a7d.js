import{j as t}from"./@nivo/heatmap-ba1ecfff.js";import{b as p,f as q,r as a,j as K}from"./vendor-851db8c1.js";import{u as V}from"./react-hook-form-687afde5.js";import{o as z}from"./yup-2824f222.js";import{c as J,a as d}from"./yup-54691517.js";import{M as Q,A as W,G as X,t as Y,d as Z,b as ee}from"./index-12adfaa3.js";import"./react-quill-73fb9518.js";/* empty css                   */import{M as l}from"./MkdInput-3424cacd.js";import{S as te}from"./index-02625b16.js";import"./@craftjs/core-d3c11b68.js";import"./@fortawesome/react-fontawesome-13437837.js";import"./@fortawesome/fontawesome-svg-core-4fa3e289.js";import"./@hookform/resolvers-67648cca.js";import"./react-confirm-alert-cd7ccfe7.js";import"./@tanstack/react-query-20158223.js";import"./@stripe/stripe-js-6b714a86.js";import"./moment-a9aaa855.js";import"./cal-heatmap-cf010ec4.js";import"./react-icons-51bc3cff.js";import"./smoothscroll-polyfill-a5c0a116.js";import"./date-fns-07266b7d.js";import"./lodash-91d5d207.js";import"./numeral-ea653b2a.js";import"./@stripe/react-stripe-js-64f0e61f.js";import"./react-select-c8303602.js";import"./@mantine/core-8cbffb6d.js";import"./@emotion/react-89b506c3.js";import"./@emotion/cache-9a5b99cd.js";import"./@emotion/utils-8a8f62c5.js";import"./@emotion/serialize-460cad7f.js";import"./@uppy/dashboard-4a19149e.js";import"./@fullcalendar/core-8ccc1ac4.js";import"./@uppy/core-0760343f.js";import"./@uppy/aws-s3-c5961f7a.js";import"./@uppy/compressor-11f993e4.js";import"./@headlessui/react-a5400090.js";import"./@fortawesome/free-solid-svg-icons-0a9c4907.js";import"./@fortawesome/free-regular-svg-icons-0a88e957.js";import"./@fortawesome/free-brands-svg-icons-fae0dcac.js";let y=new Q;const tt=c=>{var S,j,v,w;const{dispatch:I}=p.useContext(W),E=J({sport_id:d(),type:d(),ntrp:d(),reservation_id:d(),num_players:d(),num_needed:d(),need_coach:d(),notes:d(),player_ids:d(),date:d(),start_time:d(),end_time:d()}).required(),{dispatch:h}=p.useContext(X),[f,se]=p.useState({}),[N,_]=p.useState(!1),[T,x]=p.useState(!1),P=q(),[ae,R]=a.useState(0),[oe,k]=a.useState(0),[re,D]=a.useState(0),[de,C]=a.useState(0),[le,A]=a.useState(0),[ie,F]=a.useState(0),[me,B]=a.useState(0),[ne,L]=a.useState(""),[pe,M]=a.useState(""),[ce,$]=a.useState(""),[ue,G]=a.useState(""),[ye,O]=a.useState(""),{register:o,handleSubmit:H,setError:g,setValue:r,formState:{errors:s}}=V({resolver:z(E)}),m=K();a.useEffect(function(){(async function(){try{x(!0),y.setTable("buddy");const e=await y.callRestAPI({id:c.activeId?c.activeId:Number(m==null?void 0:m.id)},"GET");e.error||(r("sport_id",e.model.sport_id),r("type",e.model.type),r("ntrp",e.model.ntrp),r("reservation_id",e.model.reservation_id),r("num_players",e.model.num_players),r("num_needed",e.model.num_needed),r("need_coach",e.model.need_coach),r("notes",e.model.notes),r("player_ids",e.model.player_ids),r("date",e.model.date),r("start_time",e.model.start_time),r("end_time",e.model.end_time),R(e.model.sport_id),k(e.model.type),D(e.model.ntrp),C(e.model.reservation_id),A(e.model.num_players),F(e.model.num_needed),B(e.model.need_coach),L(e.model.notes),M(e.model.player_ids),$(e.model.date),G(e.model.start_time),O(e.model.end_time),x(!1))}catch(e){x(!1),console.log("error",e),Y(I,e.message)}})()},[]);const U=async e=>{_(!0);try{y.setTable("buddy");for(let u in f){let n=new FormData;n.append("file",f[u].file);let b=await y.uploadImage(n);e[u]=b.url}const i=await y.callRestAPI({id:c.activeId?c.activeId:Number(m==null?void 0:m.id),sport_id:e.sport_id,type:e.type,ntrp:e.ntrp,reservation_id:e.reservation_id,num_players:e.num_players,num_needed:e.num_needed,need_coach:e.need_coach,notes:e.notes,player_ids:e.player_ids,date:e.date,start_time:e.start_time,end_time:e.end_time},"PUT");if(!i.error)ee(h,"Updated"),P("/club/buddy"),h({type:"REFRESH_DATA",payload:{refreshData:!0}}),c.setSidebar(!1);else if(i.validation){const u=Object.keys(i.validation);for(let n=0;n<u.length;n++){const b=u[n];g(b,{type:"manual",message:i.validation[b]})}}_(!1)}catch(i){_(!1),console.log("Error",i),g("sport_id",{type:"manual",message:i.message})}};return p.useEffect(()=>{h({type:"SETPATH",payload:{path:"buddy"}})},[]),t.jsxs("div",{className:" shadow-md rounded   mx-auto p-5",children:[t.jsx("h4",{className:"text-2xl font-medium",children:"Edit Buddy"}),T?t.jsx(te,{}):t.jsxs("form",{className:" w-full max-w-lg",onSubmit:H(U),children:[t.jsx(l,{type:"number",page:"edit",name:"sport_id",errors:s,label:"Sport Id",placeholder:"Sport Id",register:o,className:""}),t.jsx(l,{type:"number",page:"edit",name:"type",errors:s,label:"Type",placeholder:"Type",register:o,className:""}),t.jsx(l,{type:"number",page:"edit",name:"ntrp",errors:s,label:"Ntrp",placeholder:"Ntrp",register:o,className:""}),t.jsx(l,{type:"number",page:"edit",name:"reservation_id",errors:s,label:"Reservation Id",placeholder:"Reservation Id",register:o,className:""}),t.jsx(l,{type:"number",page:"edit",name:"num_players",errors:s,label:"Num Players",placeholder:"Num Players",register:o,className:""}),t.jsx(l,{type:"number",page:"edit",name:"num_needed",errors:s,label:"Num Needed",placeholder:"Num Needed",register:o,className:""}),t.jsx(l,{type:"number",page:"edit",name:"need_coach",errors:s,label:"Need Coach",placeholder:"Need Coach",register:o,className:""}),t.jsxs("div",{className:"mb-4  ",children:[t.jsx("label",{className:"block text-gray-700 text-sm font-bold mb-2",htmlFor:"notes",children:"Notes"}),t.jsx("textarea",{placeholder:"Notes",...o("notes"),className:`shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline ${(S=s.notes)!=null&&S.message?"border-red-500":""}`,row:50}),t.jsx("p",{className:"text-red-500 text-xs italic",children:(j=s.notes)==null?void 0:j.message})]}),t.jsxs("div",{className:"mb-4  ",children:[t.jsx("label",{className:"block text-gray-700 text-sm font-bold mb-2",htmlFor:"player_ids",children:"Player Ids"}),t.jsx("textarea",{placeholder:"Player Ids",...o("player_ids"),className:`shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline ${(v=s.player_ids)!=null&&v.message?"border-red-500":""}`,row:50}),t.jsx("p",{className:"text-red-500 text-xs italic",children:(w=s.player_ids)==null?void 0:w.message})]}),t.jsx(l,{type:"date",page:"edit",name:"date",errors:s,label:"Date",placeholder:"Date",register:o,className:""}),t.jsx(l,{page:"edit",name:"start_time",errors:s,label:"Start Time",placeholder:"Start Time",register:o,className:""}),t.jsx(l,{page:"edit",name:"end_time",errors:s,label:"End Time",placeholder:"End Time",register:o,className:""}),t.jsx(Z,{type:"submit",className:"bg-primaryBlue text-white font-bold py-2 px-4 rounded focus:outline-none focus:shadow-outline",loading:N,disable:N,children:"Submit"})]})]})};export{tt as default};
