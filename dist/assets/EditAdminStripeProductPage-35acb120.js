import{j as t}from"./@nivo/heatmap-ba1ecfff.js";import{b as m,f as A,j as $,r as C}from"./vendor-851db8c1.js";import{u as D}from"./react-hook-form-687afde5.js";import{o as q}from"./yup-2824f222.js";import{c as F,a as f,b as T}from"./yup-54691517.js";import{M as G,A as I,G as M,t as g,b as y}from"./index-12adfaa3.js";import"./@craftjs/core-d3c11b68.js";import"./@fortawesome/react-fontawesome-13437837.js";import"./@fortawesome/fontawesome-svg-core-4fa3e289.js";import"./@hookform/resolvers-67648cca.js";import"./react-confirm-alert-cd7ccfe7.js";import"./@tanstack/react-query-20158223.js";import"./@stripe/stripe-js-6b714a86.js";import"./moment-a9aaa855.js";import"./cal-heatmap-cf010ec4.js";import"./react-icons-51bc3cff.js";import"./smoothscroll-polyfill-a5c0a116.js";import"./date-fns-07266b7d.js";import"./lodash-91d5d207.js";import"./numeral-ea653b2a.js";import"./@stripe/react-stripe-js-64f0e61f.js";import"./react-select-c8303602.js";import"./@mantine/core-8cbffb6d.js";import"./@emotion/react-89b506c3.js";import"./@emotion/cache-9a5b99cd.js";import"./@emotion/utils-8a8f62c5.js";import"./@emotion/serialize-460cad7f.js";import"./@uppy/dashboard-4a19149e.js";import"./@fullcalendar/core-8ccc1ac4.js";import"./@uppy/core-0760343f.js";import"./@uppy/aws-s3-c5961f7a.js";import"./@uppy/compressor-11f993e4.js";import"./@headlessui/react-a5400090.js";import"./@fortawesome/free-solid-svg-icons-0a9c4907.js";import"./@fortawesome/free-regular-svg-icons-0a88e957.js";import"./@fortawesome/free-brands-svg-icons-fae0dcac.js";let j=new G;const we=()=>{var d,p,u,x;const w=F({name:f().required(),description:f().nullable(),status:T().required()}).required(),{dispatch:c}=m.useContext(I),{dispatch:a}=m.useContext(M),N=A(),s=$(),[R,v]=C.useState(0),{register:i,handleSubmit:S,setError:k,setValue:n,formState:{errors:r}}=D({resolver:q(w)}),E=[{key:"0",value:"Inactive"},{key:"1",value:"Active"}],P=async e=>{try{console.log(e);const o=await j.updateStripeProduct(s==null?void 0:s.id,{name:e.name,description:e.description,status:e.status});if(!o.error)y(a,"Edited",4e3),N("/admin/product");else if(o.validation){const h=Object.keys(o.validation);for(let l=0;l<h.length;l++){const b=h[l];k(b,{type:"manual",message:o.validation[b]})}}}catch(o){console.log("Error",o),y(a,o.message,4e3),g(c,o.message)}};return m.useEffect(()=>{a({type:"SETPATH",payload:{path:"products"}}),async function(){try{const e=await j.getStripeProduct(s==null?void 0:s.id);if(!e.error){const o=e.model.object;n("name",o.name),n("description",o.description),n("status",e.model.status),v(e.model.id)}}catch(e){console.log("Error",e),g(c,e.message)}}()},[]),t.jsxs("div",{className:" shadow-md rounded   mx-auto p-5",children:[t.jsx("h4",{className:"text-2xl font-medium",children:"Edit Product"}),t.jsxs("form",{className:"w-full max-w-lg",onSubmit:S(P),children:[t.jsxs("div",{className:"mb-4",children:[t.jsx("label",{className:"block text-gray-700 text-sm font-bold mb-2",htmlFor:"name",children:"Name"}),t.jsx("input",{type:"text",placeholder:"Name",...i("name"),className:`"shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline ${(d=r.name)!=null&&d.message?"border-red-500":""}`}),t.jsx("p",{className:"text-red-500 text-xs italic",children:(p=r.name)==null?void 0:p.message})]}),t.jsxs("div",{className:"mb-4",children:[t.jsx("label",{className:"block text-gray-700 text-sm font-bold mb-2",htmlFor:"description",children:"Description"}),t.jsx("input",{type:"text",placeholder:"Description",...i("description"),className:`shadow appearance-none border  rounded w-full py-2 px-3 text-gray-700 mb-3 leading-tight focus:outline-none focus:shadow-outline ${(u=r.description)!=null&&u.message?"border-red-500":""}`}),t.jsx("p",{className:"text-red-500 text-xs italic",children:(x=r.description)==null?void 0:x.message})]}),t.jsxs("div",{className:"mb-4",children:[t.jsx("label",{className:"block text-gray-700 text-sm font-bold mb-2",children:"Status"}),t.jsx("select",{className:"shadow appearance-none border  rounded w-full py-2 px-3 text-gray-700 mb-3 leading-tight focus:outline-none focus:shadow-outline",...i("status"),children:E.map(e=>t.jsx("option",{value:e.key,children:e.value},e.key))})]}),t.jsx("button",{type:"submit",className:"bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded focus:outline-none focus:shadow-outline",children:"Submit"})]})]})};export{we as default};
