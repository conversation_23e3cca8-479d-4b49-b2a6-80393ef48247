import{j as a}from"./@nivo/heatmap-ba1ecfff.js";import{b as s,u as L,r as e,f as E}from"./vendor-851db8c1.js";import{u as w}from"./react-hook-form-687afde5.js";import{o as C}from"./yup-2824f222.js";import{c as R,a as t}from"./yup-54691517.js";import{M as T,G as _,u as j,A as v,e as G}from"./index-12adfaa3.js";import"./react-quill-73fb9518.js";/* empty css                   */import"./index-02625b16.js";import"./Calendar-9031b5fe.js";import"./BackButton-11ba52b2.js";import{A as P}from"./AddBuddy-7f829cd8.js";import"./@craftjs/core-d3c11b68.js";import"./@fortawesome/react-fontawesome-13437837.js";import"./@fortawesome/fontawesome-svg-core-4fa3e289.js";import"./@hookform/resolvers-67648cca.js";import"./react-confirm-alert-cd7ccfe7.js";import"./@tanstack/react-query-20158223.js";import"./@stripe/stripe-js-6b714a86.js";import"./moment-a9aaa855.js";import"./cal-heatmap-cf010ec4.js";import"./react-icons-51bc3cff.js";import"./smoothscroll-polyfill-a5c0a116.js";import"./date-fns-07266b7d.js";import"./lodash-91d5d207.js";import"./numeral-ea653b2a.js";import"./@stripe/react-stripe-js-64f0e61f.js";import"./react-select-c8303602.js";import"./@mantine/core-8cbffb6d.js";import"./@emotion/react-89b506c3.js";import"./@emotion/cache-9a5b99cd.js";import"./@emotion/utils-8a8f62c5.js";import"./@emotion/serialize-460cad7f.js";import"./@uppy/dashboard-4a19149e.js";import"./@fullcalendar/core-8ccc1ac4.js";import"./@uppy/core-0760343f.js";import"./@uppy/aws-s3-c5961f7a.js";import"./@uppy/compressor-11f993e4.js";import"./@headlessui/react-a5400090.js";import"./@fortawesome/free-solid-svg-icons-0a9c4907.js";import"./@fortawesome/free-regular-svg-icons-0a88e957.js";import"./@fortawesome/free-brands-svg-icons-fae0dcac.js";import"./react-tooltip-7a26650a.js";import"./ChevronLeftIcon-e5eecf9c.js";import"./ChevronRightIcon-efb4c46c.js";import"./SuccessModal-e9ef416e.js";import"./TimeSlots-4d6eb2b6.js";import"./SportTypeSelection-ee0cc3da.js";import"./SelectionOptionsCard-0d5c6ddd.js";import"./SelectionOption-01b973e9.js";import"./ReservationSummary-7084c977.js";let r=new T;const Ut=({setSidebar:I})=>{const{dispatch:p,state:U}=s.useContext(_),{club:c,sports:l}=j(),u=L();console.log("locationss",u);const n=R({club_id:t(),user_id:t(),sport:t(),level:t(),start_time:t(),end_time:t(),num_players_needed:t(),players:t(),bio:t(),status:t()}).required();s.useContext(v),s.useState({}),s.useState(!1),s.useState(!1);const[d,i]=e.useState(!1);E(),w({resolver:C(n)});const[f,S]=s.useState([]),[k,b]=e.useState(null),[q,m]=e.useState(!1),[h,x]=e.useState([]),g=async()=>{m(!0);try{const o=await r.callRawAPI("/v3/api/custom/courtmatchup/club/profile",{},"GET");r.setTable("user");const A=await r.callRestAPI({filter:["role,cs,user"]},"GETALL");r.setTable("user");const y=await r.callRestAPI({filter:["role,cs,coach"]},"GETALL");S(A.list),x(y.list),b(o==null?void 0:o.model)}catch(o){return console.error("Error fetching users:",o),[]}finally{m(!1)}};return e.useEffect(()=>{(async()=>(i(!0),await g(),i(!1)))()},[]),s.useEffect(()=>{p({type:"SETPATH",payload:{path:"find-a-buddy"}})},[]),a.jsxs("div",{className:"mx-auto max-w-7xl p-4",children:[d&&a.jsx(G,{}),a.jsx(P,{users:f,coaches:h,role:"club",club:c,sports:l})]})};export{Ut as default};
