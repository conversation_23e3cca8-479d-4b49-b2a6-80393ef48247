import{j as t}from"./@nivo/heatmap-ba1ecfff.js";import{M as u,m as h}from"./index-12adfaa3.js";import{b as s,u as o}from"./vendor-851db8c1.js";let p=new u;const y=({children:r})=>{var n;const[e,l]=s.useState(null),c=o().pathname,m=o();localStorage.getItem("user_id");const i=new URLSearchParams(m.search).get("club_id");s.useEffect(()=>{h({path:c,clubName:e==null?void 0:e.name,favicon:e==null?void 0:e.club_logo})},[c,(n=e==null?void 0:e.club)==null?void 0:n.name,e==null?void 0:e.club_logo]);async function d(){try{const a=await p.callRawAPI(`/v3/api/custom/courtmatchup/user/club/${i}`,{},"GET");l(a.model)}catch(a){console.log(a)}}return s.useEffect(()=>{d()},[]),t.jsxs("div",{className:"flex min-h-screen flex-col justify-between bg-white",children:[t.jsxs("header",{className:"mt-5 flex w-full flex-wrap items-center justify-between gap-10 px-10 text-center max-md:max-w-full lg:px-20",children:[t.jsx("div",{className:"my-auto flex items-center gap-2 self-stretch text-2xl font-bold leading-none tracking-tight text-gray-950",children:(e==null?void 0:e.club_logo)&&t.jsx("img",{loading:"lazy",src:(e==null?void 0:e.club_logo)||"/courtmatchup-logo.png",alt:"",className:"my-auto aspect-square w-32 shrink-0 self-stretch object-contain"})}),t.jsx("div",{className:"my-auto flex items-center gap-4 self-stretch whitespace-nowrap text-sm font-medium leading-none tracking-normal text-gray-600",children:t.jsxs("button",{className:"my-auto flex items-center justify-center gap-1 self-stretch overflow-hidden rounded-xl border border-solid border-zinc-200 bg-white p-2.5 shadow-sm",children:[t.jsx("img",{loading:"lazy",src:"https://cdn.builder.io/api/v1/image/assets/TEMP/378ba133bc954cd4a2fb13f69472d2c81e693904f63c2c67b519ec575f18f637?placeholderIfAbsent=true&apiKey=0441163c08384504abd85a50d31ebaa1",alt:"",className:"my-auto aspect-square w-5 shrink-0 self-stretch object-contain"}),t.jsx("span",{className:"my-auto gap-2 self-stretch px-1",children:"Support"})]})})]}),r,t.jsxs("footer",{className:"z-10 mt-10 flex items-center justify-center gap-2  py-4 text-center text-sm font-medium leading-none tracking-normal text-gray-600",children:[t.jsx("p",{children:"Powered by"}),t.jsx("img",{loading:"lazy",src:"/courtmatchup-logo.png",alt:"",className:"my-auto aspect-square w-28 shrink-0 self-stretch object-contain"})]})]})};export{y as A};
