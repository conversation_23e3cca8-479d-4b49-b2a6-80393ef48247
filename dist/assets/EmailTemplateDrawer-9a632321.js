import{j as e}from"./@nivo/heatmap-ba1ecfff.js";import{r as l}from"./vendor-851db8c1.js";import{S as D}from"./react-select-c8303602.js";import{M as W,G as Z,a5 as z,d as J,b as _}from"./index-12adfaa3.js";import{S as K}from"./SuccessModal-e9ef416e.js";let q=new W;const ee=({isOpen:E,onClose:g,members:x})=>{if(!E)return null;const[m,f]=l.useState([]),[j,F]=l.useState(""),[h,v]=l.useState("schedule"),[I,N]=l.useState(!1),{dispatch:R}=l.useContext(Z),[a,n]=l.useState({subject:"",message:"",date:"",time:"",repeat:0,type:0,days:[],end_date:"",condition:{before:0,time:30,condition_type:0,time_type:1},attachments:[],players:[]}),[T,w]=l.useState(!1),[y,P]=l.useState([]),[i,C]=l.useState(null),[O,b]=l.useState(null),[A,S]=l.useState([]),[B,k]=l.useState(!1),[r,c]=l.useState({gender:"",ntrp:"",country:"",city:""}),G=[{label:"M",number:1,value:"monday"},{label:"T",number:2,value:"tuesday"},{label:"W",number:3,value:"wednesday"},{label:"T",number:4,value:"thursday"},{label:"F",number:5,value:"friday"},{label:"S",number:6,value:"saturday"},{label:"S",number:0,value:"sunday"}],M=x.filter(t=>{const s=`${t.first_name} ${t.last_name}`.toLowerCase().includes(j.toLowerCase()),d=!r.gender||(t.gender||"").toLowerCase().includes(r.gender.toLowerCase()),o=!r.ntrp||t.ntrp===r.ntrp,u=!r.country||(t.country||"").toLowerCase().includes(r.country.toLowerCase()),p=!r.city||(t.city||"").toLowerCase().includes(r.city.toLowerCase());return s&&d&&o&&u&&p}),$=t=>{n(s=>({...s,days:s.days.includes(t)?s.days.filter(d=>d!==t):[...s.days,t]}))};l.useEffect(()=>{n(t=>({...t,players:m.map(s=>{var d;return((d=x.find(o=>o.id===s))==null?void 0:d.email)||""}).filter(s=>s)}))},[m,x]);const H=async()=>{N(!0);try{const t={...a,date:a.date,repeat:parseInt(a.repeat),condition:{before:parseInt(a.condition.before),time:a.condition.time,condition_type:parseInt(a.condition.condition_type),time_type:parseInt(a.condition.time_type)}};console.log(t),await q.callRawAPI("/v3/api/custom/courtmatchup/club/reservations/scheduling",t,"POST"),_("success","Email template created successfully"),k(!0)}catch(t){_(R,t.message,3e3,"error")}finally{N(!1)}};l.useEffect(()=>{(async()=>{try{const d=await(await fetch("/countries.min.json")).json(),o=Object.keys(d).map(u=>({value:u,label:u,cities:d[u].map(p=>({value:p,label:p}))}));P(o)}catch(s){console.error("Error loading countries:",s)}})()},[]),l.useEffect(()=>{if(i){const t=y.find(s=>s.value===i.value);S((t==null?void 0:t.cities)||[])}else S([])},[i,y]);const L={control:t=>({...t,minHeight:36,borderColor:"#e5e7eb",borderRadius:"0.5rem","&:hover":{borderColor:"#e5e7eb"}}),valueContainer:t=>({...t,padding:"0 5px"}),input:t=>({...t,margin:0,padding:0})};return e.jsxs("div",{className:"fixed inset-0 z-50 overflow-hidden",children:[e.jsx("div",{className:"absolute inset-0 bg-gray-500 bg-opacity-75 transition-opacity",onClick:g}),e.jsx("div",{className:"fixed inset-x-0 bottom-0 z-10",children:e.jsx("div",{className:"flex  min-h-full items-end justify-center text-center sm:items-center ",children:e.jsxs("div",{className:"relative h-full max-h-[97vh] w-full transform overflow-hidden rounded-t-2xl bg-white text-left shadow-xl transition-all",children:[e.jsxs("div",{className:"flex items-center justify-between border-b border-gray-200 px-8 py-4",children:[e.jsx("h3",{className:"text-lg font-semibold text-gray-900",children:"Create email template"}),e.jsx("button",{onClick:g,className:"rounded-full p-2 hover:bg-gray-100",children:e.jsx("svg",{width:"20",height:"20",viewBox:"0 0 20 20",fill:"none",children:e.jsx("path",{d:"M15 5L5 15M5 5L15 15",stroke:"currentColor",strokeWidth:"1.67",strokeLinecap:"round",strokeLinejoin:"round"})})})]}),e.jsxs("div",{className:"grid max-h-[calc(90vh-80px)] grid-cols-1 gap-8 overflow-y-auto bg-gray-100 p-8 md:grid-cols-2",children:[e.jsxs("div",{className:"space-y-6 rounded-xl bg-white",children:[e.jsx("div",{className:"border-b p-4",children:e.jsx("h4",{className:"font-medium",children:"Settings"})}),e.jsxs("div",{className:"space-y-4 p-4",children:[e.jsxs("div",{className:"flex gap-4 border-b border-gray-100 pb-4",children:[e.jsx("button",{onClick:()=>v("schedule"),className:`font-medium ${h==="schedule"?"text-[#1D275F]":"text-gray-500"}`,children:"Schedule"}),e.jsx("button",{onClick:()=>v("condition"),className:`font-medium ${h==="condition"?"text-[#1D275F]":"text-gray-500 "}`,children:"Condition"})]}),h==="schedule"&&e.jsxs("div",{className:"space-y-4 rounded-lg bg-gray-50 p-4",children:[e.jsxs("div",{className:"space-y-2",children:[e.jsx("label",{className:"text-sm text-gray-600",children:"Date/time"}),e.jsxs("div",{className:"flex gap-4",children:[e.jsx("input",{type:"date",value:a.date,onChange:t=>n(s=>({...s,date:t.target.value})),className:"rounded-lg border border-gray-200 px-3 py-2 text-sm"}),e.jsx("input",{type:"time",value:a.time,onChange:t=>n(s=>({...s,time:t.target.value})),className:"rounded-lg border border-gray-200 px-3 py-2 text-sm"})]})]}),e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsxs("label",{className:"relative inline-flex cursor-pointer items-center",children:[e.jsx("input",{type:"checkbox",checked:a.repeat===1,onChange:t=>n(s=>({...s,repeat:t.target.checked?1:0})),className:"peer sr-only"}),e.jsx("div",{className:"h-6 w-11 rounded-full bg-gray-200 after:absolute after:left-[2px] after:top-[2px] after:h-5 after:w-5 after:rounded-full after:border after:border-gray-300 after:bg-white after:transition-all after:content-[''] peer-checked:bg-[#1D275F] peer-checked:after:translate-x-full peer-checked:after:border-white"})]}),e.jsx("span",{className:"text-sm text-gray-600",children:"Repeat"})]}),e.jsxs("div",{className:"flex items-center gap-3",children:[e.jsx("svg",{width:"24",height:"24",viewBox:"0 0 24 24",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:e.jsx("path",{d:"M10.75 1.5L14.25 4.75L10.75 8M13.25 16L9.75 19.25L13.25 22.5M10.75 19.25H14C18.0041 19.25 21.25 16.0041 21.25 12C21.25 9.81504 20.2834 7.85583 18.7546 6.52661M13.25 4.75H10C5.99593 4.75 2.75 7.99594 2.75 12C2.75 14.1854 3.71696 16.145 5.24638 17.4742",stroke:"#525866","stroke-width":"1.5","stroke-linecap":"round","stroke-linejoin":"round"})}),e.jsx("span",{className:"text-sm text-gray-600",children:"Repeat every"}),e.jsxs("select",{className:"rounded-lg border border-gray-200 px-3 py-2 pr-8 text-sm",children:[e.jsx("option",{children:"1"}),e.jsx("option",{children:"2"}),e.jsx("option",{children:"3"})]}),e.jsxs("select",{className:"rounded-lg border border-gray-200 px-3 py-2 pr-8 text-sm",children:[e.jsx("option",{children:"week"}),e.jsx("option",{children:"month"})]})]}),e.jsx("div",{className:"flex gap-2",children:G.map(t=>e.jsx("button",{onClick:()=>$(t.value),className:`h-8 w-8 rounded-full border border-gray-200 ${a.days.includes(t.value)?"bg-[#1D275F] text-white":"text-gray-500"}`,children:t.label},t.value))}),T?e.jsxs("div",{className:"space-y-2",children:[e.jsx("label",{className:"text-sm text-gray-600",children:"End date"}),e.jsx("input",{type:"date",value:a.end_date,onChange:t=>n(s=>({...s,end_date:t.target.value})),className:"block rounded-lg border border-gray-200 px-3 py-2 pr-8 text-sm"}),e.jsx("button",{onClick:()=>{w(!1),n(t=>({...t,end_date:""}))},className:"text-sm text-red-500",children:"Remove end date"})]}):e.jsxs("button",{onClick:()=>w(!0),className:"inline-flex items-center gap-2 text-sm text-[#1D275F] underline",children:[e.jsx("span",{children:"+"}),"Add end date"]})]}),h==="condition"&&e.jsx("div",{className:"space-y-4 rounded-lg bg-gray-50 p-4",children:e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx("select",{value:a.condition.time,onChange:t=>n(s=>({...s,condition:{...s.condition,time:parseInt(t.target.value)}})),className:"w-16 rounded-lg border border-gray-200 px-3 py-2 text-sm",children:[...Array(60)].map((t,s)=>e.jsx("option",{value:s+1,children:s+1},s))}),e.jsxs("select",{value:a.condition.time_type,onChange:t=>n(s=>({...s,condition:{...s.condition,time_type:parseInt(t.target.value)}})),className:"w-fit rounded-lg border border-gray-200 px-3 py-2 pr-8 text-sm",children:[e.jsx("option",{value:0,children:"minutes"}),e.jsx("option",{value:1,children:"hours"}),e.jsx("option",{value:2,children:"days"}),e.jsx("option",{value:3,children:"weeks"})]}),e.jsxs("select",{className:"w-fit rounded-lg border border-gray-200 px-3 py-2 pr-8 text-sm",value:a.condition.before,onChange:t=>n(s=>({...s,condition:{...s.condition,before:parseInt(t.target.value)}})),children:[e.jsx("option",{value:0,children:"before"}),e.jsx("option",{value:1,children:"after"})]})]}),e.jsxs("div",{className:"space-y-2",children:[e.jsx("label",{className:"block text-sm text-gray-600",children:"Reservation type"}),e.jsxs("select",{value:a.condition.condition_type,onChange:t=>n(s=>({...s,condition:{...s.condition,condition_type:parseInt(t.target.value)}})),className:"block w-full rounded-lg border border-gray-200 px-3 py-2 pr-8 text-sm",children:[e.jsx("option",{value:0,children:"Reservation"}),e.jsx("option",{value:1,children:"Clinic"})]})]})]})})]}),e.jsxs("div",{className:"space-y-4 p-4",children:[e.jsxs("div",{className:"flex justify-between",children:[e.jsx("p",{className:"text-lg font-medium",children:"Recipients"}),e.jsx("button",{onClick:()=>f(x.map(t=>t.id)),className:"text-sm text-blue-500",children:"Send to all"})]}),e.jsxs("div",{className:"flex w-full items-center gap-2 rounded-lg border border-gray-200 px-3 ",children:[e.jsx("svg",{width:"20",height:"20",viewBox:"0 0 20 20",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:e.jsx("path",{d:"M9.25 2.5C12.976 2.5 16 5.524 16 9.25C16 12.976 12.976 16 9.25 16C5.524 16 2.5 12.976 2.5 9.25C2.5 5.524 5.524 2.5 9.25 2.5ZM9.25 14.5C12.1502 14.5 14.5 12.1502 14.5 9.25C14.5 6.349 12.1502 4 9.25 4C6.349 4 4 6.349 4 9.25C4 12.1502 6.349 14.5 9.25 14.5ZM15.6137 14.5532L17.7355 16.6742L16.6742 17.7355L14.5532 15.6137L15.6137 14.5532Z",fill:"#525866"})}),e.jsx("input",{type:"text",placeholder:"search by name",value:j,onChange:t=>F(t.target.value),className:"h-full w-full border-none bg-transparent py-2 text-sm outline-none focus:border-none focus:!outline-none"})]}),e.jsxs("div",{className:"flex w-full justify-between gap-2",children:[e.jsx("div",{className:"flex-1",children:e.jsx(D,{value:i,onChange:t=>{C(t),b(null),c(s=>({...s,country:(t==null?void 0:t.value)||"",city:""}))},options:y,placeholder:"Country",styles:L,className:"text-sm"})}),e.jsx("div",{className:"flex-1",children:e.jsx(D,{value:O,onChange:t=>{b(t),c(s=>({...s,city:(t==null?void 0:t.value)||""}))},options:A,placeholder:"City",isDisabled:!i,styles:L,className:"text-sm"})}),e.jsxs("select",{className:"flex-1 rounded-lg border border-gray-200 px-3 py-2 text-sm",value:r.gender,onChange:t=>c(s=>({...s,gender:t.target.value})),children:[e.jsx("option",{value:"",children:"Gender"}),e.jsx("option",{value:"male",children:"Male"}),e.jsx("option",{value:"female",children:"Female"})]}),e.jsxs("select",{className:"flex-1 rounded-lg border border-gray-200 px-3 py-2 text-sm",value:r.ntrp,onChange:t=>c(s=>({...s,ntrp:t.target.value})),children:[e.jsx("option",{value:"",children:"NTRP"}),z.map(t=>e.jsx("option",{value:t.toString(),children:t},t))]})]}),(r.gender||r.ntrp||r.country||r.city)&&e.jsx("button",{onClick:()=>{c({gender:"",ntrp:"",country:"",city:""}),C(null),b(null)},className:"mt-2 text-sm text-blue-500",children:"Clear filters"}),e.jsxs("div",{className:"max-h-[300px] space-y-3 overflow-y-auto rounded-lg bg-gray-50 p-4",children:[M.map((t,s)=>e.jsxs("label",{className:"flex items-center gap-3 rounded-lg p-2 hover:bg-gray-50",children:[e.jsx("input",{type:"checkbox",checked:m.includes(t.id),onChange:()=>f(d=>d.includes(t.id)?d.filter(o=>o!==t.id):[...d,t.id]),className:"h-4 w-4 rounded border-gray-300 text-[#1D275F] focus:ring-[#1D275F]"}),e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx("div",{className:"h-8 w-8 overflow-hidden rounded-full bg-gray-100",children:e.jsx("img",{src:(t==null?void 0:t.photo)||"/default-avatar.png",alt:t.first_name,className:"h-full w-full object-cover"})}),e.jsxs("span",{className:"text-sm text-gray-700",children:[t.first_name," ",t.last_name]})]})]},s)),M.length===0&&e.jsx("p",{className:"text-center text-sm text-gray-500",children:"No recipients found"})]})]}),e.jsxs("div",{className:"space-y-4 p-4",children:[e.jsx("p",{className:"text-lg font-medium",children:"Attachment"}),e.jsxs("div",{className:"rounded-lg border border-dashed border-gray-200 p-4 text-center",children:[e.jsx("p",{className:"text-sm text-gray-500",children:"Choose a file or drag & drop it here."}),e.jsx("p",{className:"text-xs text-gray-400",children:"JPEG, PNG, PDF, and MP4 formats, up to 50 MB."}),e.jsx("button",{className:"mt-2 rounded-lg border border-gray-200 px-4 py-2 text-sm",children:"Browse File"})]})]})]}),e.jsxs("div",{className:"h-fit space-y-6 rounded-xl bg-white",children:[e.jsx("div",{className:"border-b p-4",children:e.jsx("h4",{className:"font-medium",children:"Email"})}),e.jsxs("div",{className:"p-4",children:[e.jsxs("div",{className:"space-y-4 p-4",children:[e.jsx("input",{type:"text",placeholder:"Subject",value:a.subject,onChange:t=>n(s=>({...s,subject:t.target.value})),className:"w-full rounded-lg border border-gray-200 px-3 py-2 text-sm"}),e.jsx("textarea",{placeholder:"Message",value:a.message,onChange:t=>n(s=>({...s,message:t.target.value})),className:"h-[200px] w-full rounded-lg border border-gray-200 px-3 py-2 text-sm"})]}),e.jsx(J,{onClick:H,loading:I,className:"mx-4 mt-2 w-fit rounded-lg bg-[#1D275F] px-4 py-2 text-sm text-white",children:"Send"})]})]})]})]})})}),B&&e.jsx(K,{onContinue:()=>{k(!1),g()},description:"Email successfully sent to "+m.length+" recipients",title:"Email sent!"})]})};export{ee as E};
