import{j as e}from"./@nivo/heatmap-ba1ecfff.js";import{u as xe}from"./react-hook-form-687afde5.js";import{o as pe}from"./yup-2824f222.js";import{c as he}from"./yup-54691517.js";import{r as o,k as be,u as ge}from"./vendor-851db8c1.js";import{G as ye,e as fe,K as ve,ax as Ce,ay as je,az as we,d as Ne,a5 as Me,M as _,b as g,H as Le,E as Ze,J as Ve}from"./index-12adfaa3.js";const j=[{value:"first_name",label:"First Name",type:"text"},{value:"last_name",label:"Last Name",type:"text"},{value:"email",label:"Email",type:"email"},{value:"password",label:"Password",type:"password"},{value:"phone",label:"Phone Number",type:"tel"},{value:"gender",label:"Gender",type:"select",options:["Male","Female","Other"]},{value:"ntrp",label:"NTRP Rating",type:"select",options:Me}],q=[{value:"text",label:"Text"},{value:"email",label:"Email"},{value:"tel",label:"Phone"},{value:"number",label:"Number"},{value:"select",label:"Select"},{value:"date",label:"Date"}];function Pe({club:c}){const{dispatch:u,state:He}=o.useContext(ye),[y,f]=o.useState(j.map(s=>({...s,required:!0}))),[Z,w]=o.useState(j),[V,H]=o.useState(null),[i,x]=o.useState({value:"",label:"",type:"text",options:[],min:"",max:"",step:"1"}),[d,N]=o.useState({value:"",label:"",type:"text",options:[],min:"",max:"",step:"1"}),[I,k]=o.useState(!1),[ke,P]=o.useState(null),[R,B]=o.useState(!1),[U]=be(),F=U.get("club_id"),[Be,G]=o.useState(!1),[p,M]=o.useState(""),[J,S]=o.useState(!1),[h,E]=o.useState(null),[K,O]=o.useState(!1),$=localStorage.getItem("user"),T=localStorage.getItem("role"),z=he().shape({}),{state:b}=ge(),{register:Fe,handleSubmit:Q,control:Se,formState:{errors:Ee},setValue:De}=xe({resolver:pe(z)}),W=s=>{f(t=>t.findIndex(l=>l.value===s.value)>-1?t.filter(l=>l.value!==s.value):[...t,{...s,required:!1}])},X=async()=>{var a;B(!0);const s=new _;s.setTable("clubs");const t=F||(T==="admin"?b==null?void 0:b.clubId:c==null?void 0:c.id);if(!t){g(u,"Club ID not found",4e3,"error"),B(!1);return}try{const l=await s.callRestAPI({id:t},"GET");if(P(l.model),(a=l.model)!=null&&a.custom_fields)try{const n=JSON.parse(l.model.custom_fields),r=[...j];n.forEach(m=>{j.some(L=>L.value===m.value)||r.push(m)}),f(r.map(m=>{var L;return{...m,required:((L=n.find(ue=>ue.value===m.value))==null?void 0:L.required)??!0}}));const D=Z.map(m=>m.value),A=n.filter(m=>!D.includes(m.value));A.length>0&&w(m=>[...m,...A])}catch(n){console.error("Error parsing custom fields:",n),g(u,"Error parsing custom fields",4e3,"error")}}catch(l){console.error("Error fetching club data:",l),g(u,"Error fetching club data",4e3,"error")}finally{B(!1)}},Y=s=>{f(t=>t.map(a=>a.value===s.value?{...a,required:!a.required}:a))},v=s=>{const{name:t,value:a}=s.target;N(l=>{const n={...l,[t]:a};return t==="label"&&(n.value=a.replace(/\s+/g,"_").toLowerCase()),t==="type"&&(n.options=a==="select"?[]:l.options),n})},ee=()=>{p.trim()&&(N(s=>({...s,options:[...s.options,p.trim()]})),M(""))},se=s=>{N(t=>({...t,options:t.options.filter(a=>a!==s)}))},te=()=>{d.value&&d.label&&(w([...Z,d]),N({value:"",label:"",type:"text",options:[],min:"",max:"",step:"1"}),G(!1))},ae=async s=>{k(!0);try{const t=new _;t.setTable("clubs");const a=F||(T==="admin"?b==null?void 0:b.clubId:c==null?void 0:c.id);if(!a){g(u,"Club ID not found",4e3,"error"),k(!1);return}const l=await t.callRestAPI({id:a,custom_fields:JSON.stringify(y)},"PUT");await Le(t,{user_id:$,activity_type:Ze.club_ui,action_type:Ve.UPDATE,data:y,club_id:c==null?void 0:c.id,description:"Updated custom sign up form"}),l.error||g(u,"Form settings saved successfully")}catch(t){console.error("Error saving form settings:",t),g(u,"Error saving form settings",4e3,"error")}finally{k(!1)}},le=s=>{H(s.value),x({...s,options:s.options||[],min:s.min||"",max:s.max||"",step:s.step||"1"})},re=()=>{i.value&&i.label&&(w(s=>s.map(t=>t.value===V?{...i}:t)),f(s=>s.map(t=>t.value===V?{...i,required:t.required}:t)),H(null),x({value:"",label:"",type:"text",options:[],min:"",max:"",step:"1"}))},ne=()=>{H(null),x({value:"",label:"",type:"text",options:[],min:"",max:"",step:"1"})},C=s=>{const{name:t,value:a}=s.target;x(l=>{const n={...l,[t]:a};return t==="type"&&(n.options=a==="select"?l.options:[]),n})},oe=()=>{p.trim()&&(x(s=>({...s,options:[...s.options,p.trim()]})),M(""))},ie=s=>{x(t=>({...t,options:t.options.filter(a=>a!==s)}))},de=s=>{E(s),S(!0)},me=()=>{O(!0),h&&(w(s=>s.filter(t=>t.value!==h.value)),f(s=>s.filter(t=>t.value!==h.value)),O(!1),S(!1),E(null))},ce=()=>{S(!1),E(null)};return o.useEffect(()=>{X()},[F]),R?e.jsx(fe,{}):e.jsxs("div",{className:"max-w-4xl rounded-xl bg-white p-6 ",children:[e.jsx("h1",{className:"mb-6 text-2xl font-bold text-gray-900",children:"Custom Sign Up Form"}),e.jsx(ve,{isOpen:J,onClose:ce,onDelete:me,title:"Delete Field",message:`Are you sure you want to delete the field "${h==null?void 0:h.label}"?`,loading:K,buttonText:"Delete Field"}),e.jsxs("form",{onSubmit:Q(ae),className:"space-y-6",children:[e.jsxs("div",{className:"space-y-4",children:[e.jsx("h2",{className:"text-lg font-medium text-gray-900",children:"Form Fields"}),Z.map((s,t)=>{var n;const a=j.some(r=>r.value===s.value),l=V===s.value;return e.jsx("div",{className:"rounded-xl bg-gray-50 p-4",children:l?e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{className:"grid grid-cols-2 gap-4",children:[e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700",children:"Field Label"}),e.jsx("input",{type:"text",name:"label",value:i.label,onChange:C,className:"mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-primaryBlue focus:ring-primaryBlue sm:text-sm"})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700",children:"Field Type"}),e.jsx("select",{name:"type",value:i.type,onChange:C,className:"mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-primaryBlue focus:ring-primaryBlue sm:text-sm",children:q.map(r=>e.jsx("option",{value:r.value,children:r.label},r.value))})]})]}),i.type==="number"&&e.jsxs("div",{className:"grid grid-cols-3 gap-4",children:[e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700",children:"Min"}),e.jsx("input",{type:"number",name:"min",value:i.min,onChange:C,className:"mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-primaryBlue focus:ring-primaryBlue sm:text-sm"})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700",children:"Max"}),e.jsx("input",{type:"number",name:"max",value:i.max,onChange:C,className:"mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-primaryBlue focus:ring-primaryBlue sm:text-sm"})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700",children:"Step"}),e.jsx("input",{type:"number",name:"step",value:i.step,onChange:C,className:"mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-primaryBlue focus:ring-primaryBlue sm:text-sm"})]})]}),i.type==="select"&&e.jsxs("div",{className:"space-y-2",children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700",children:"Options"}),e.jsxs("div",{className:"flex space-x-2",children:[e.jsx("input",{type:"text",value:p,onChange:r=>M(r.target.value),placeholder:"Add option",className:"block w-full rounded-md border-gray-300 shadow-sm focus:border-primaryBlue focus:ring-primaryBlue sm:text-sm"}),e.jsx("button",{type:"button",onClick:oe,className:"inline-flex items-center rounded-md border border-transparent bg-primaryBlue px-3 py-2 text-sm font-medium text-white shadow-sm hover:bg-primaryBlue/80",children:"Add"})]}),e.jsx("div",{className:"mt-2 space-y-2",children:i.options.map((r,D)=>e.jsxs("div",{className:"flex items-center justify-between rounded bg-gray-100 p-2",children:[e.jsx("span",{children:r}),e.jsx("button",{type:"button",onClick:()=>ie(r),className:"text-red-500 hover:text-red-700",children:e.jsx("svg",{width:"24",height:"24",viewBox:"0 0 24 24",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:e.jsx("path",{d:"M5.68964 20.3144L4.94119 20.3627L5.68964 20.3144ZM18.3104 20.3144L19.0588 20.3627V20.3627L18.3104 20.3144ZM2.75 5C2.33579 5 2 5.33579 2 5.75C2 6.16421 2.33579 6.5 2.75 6.5V5ZM21.25 6.5C21.6642 6.5 22 6.16421 22 5.75C22 5.33579 21.6642 5 21.25 5V6.5ZM10.5 10.75C10.5 10.3358 10.1642 10 9.75 10C9.33579 10 9 10.3358 9 10.75H10.5ZM9 16.25C9 16.6642 9.33579 17 9.75 17C10.1642 17 10.5 16.6642 10.5 16.25H9ZM15 10.75C15 10.3358 14.6642 10 14.25 10C13.8358 10 13.5 10.3358 13.5 10.75H15ZM13.5 16.25C13.5 16.6642 13.8358 17 14.25 17C14.6642 17 15 16.6642 15 16.25H13.5ZM15.1477 5.93694C15.2509 6.33808 15.6598 6.57957 16.0609 6.47633C16.4621 6.37308 16.7036 5.9642 16.6003 5.56306L15.1477 5.93694ZM4.00156 5.79829L4.94119 20.3627L6.43808 20.2661L5.49844 5.70171L4.00156 5.79829ZM6.68756 22H17.3124V20.5H6.68756V22ZM19.0588 20.3627L19.9984 5.79829L18.5016 5.70171L17.5619 20.2661L19.0588 20.3627ZM19.25 5H4.75V6.5H19.25V5ZM2.75 6.5H4.75V5H2.75V6.5ZM19.25 6.5H21.25V5H19.25V6.5ZM17.3124 22C18.2352 22 18.9994 21.2835 19.0588 20.3627L17.5619 20.2661C17.5534 20.3976 17.4443 20.5 17.3124 20.5V22ZM4.94119 20.3627C5.0006 21.2835 5.76481 22 6.68756 22V20.5C6.55574 20.5 6.44657 20.3976 6.43808 20.2661L4.94119 20.3627ZM9 10.75V16.25H10.5V10.75H9ZM13.5 10.75V16.25H15V10.75H13.5ZM12 3.5C13.5134 3.5 14.7868 4.53504 15.1477 5.93694L16.6003 5.56306C16.0731 3.51451 14.2144 2 12 2V3.5ZM8.85237 5.93694C9.21319 4.53504 10.4867 3.5 12 3.5V2C9.78568 2 7.92697 3.51451 7.39971 5.56306L8.85237 5.93694Z",fill:"#868C98"})})})]},D))})]}),e.jsxs("div",{className:"flex justify-end space-x-2",children:[e.jsxs("button",{type:"button",onClick:re,className:"inline-flex items-center rounded-md border border-transparent bg-primaryBlue px-3 py-2 text-sm font-medium text-white shadow-sm hover:bg-primaryBlue/80",children:[e.jsx(Ce,{className:"mr-2"}),"Save"]}),e.jsxs("button",{type:"button",onClick:ne,className:"inline-flex items-center rounded-md border border-gray-300 bg-white px-3 py-2 text-sm font-medium text-gray-700 shadow-sm hover:bg-gray-50",children:[e.jsx(je,{className:"mr-2"}),"Cancel"]})]})]}):e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{className:"flex items-center space-x-4",children:[e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsx("input",{type:"checkbox",checked:y.some(r=>r.value===s.value),onChange:()=>W(s),className:"rounded border-gray-300 text-primaryBlue focus:ring-primaryBlue"}),e.jsxs("label",{className:"text-sm font-medium text-gray-700",children:[s.label,e.jsxs("span",{className:"ml-2 text-xs text-gray-500",children:["(",s.type,")"]})]})]}),y.some(r=>r.value===s.value)&&e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsx("input",{type:"checkbox",checked:((n=y.find(r=>r.value===s.value))==null?void 0:n.required)||!1,onChange:()=>Y(s),className:"rounded border-gray-300 text-primaryBlue focus:ring-primaryBlue"}),e.jsx("span",{className:"text-sm text-gray-600",children:"Required"})]})]}),e.jsx("div",{className:"flex items-center space-x-2",children:!a&&e.jsxs(e.Fragment,{children:[e.jsx("button",{type:"button",onClick:()=>le(s),className:"p-1 text-gray-500 hover:text-gray-700",children:e.jsx("svg",{width:"24",height:"24",viewBox:"0 0 24 24",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:e.jsx("path",{d:"M13.25 6.24985L16.2929 3.20696C16.6834 2.81643 17.3166 2.81643 17.7071 3.20696L20.7929 6.29274C21.1834 6.68327 21.1834 7.31643 20.7929 7.70696L17.75 10.7498M13.25 6.24985L3.04289 16.457C2.85536 16.6445 2.75 16.8988 2.75 17.1641V21.2498H6.83579C7.101 21.2498 7.35536 21.1445 7.54289 20.957L17.75 10.7498M13.25 6.24985L17.75 10.7498",stroke:"#868C98","stroke-width":"1.5","stroke-linecap":"round","stroke-linejoin":"round"})})}),e.jsx("button",{type:"button",onClick:()=>de(s),className:"p-1 text-red-500 hover:text-red-700",children:e.jsx("svg",{width:"24",height:"24",viewBox:"0 0 24 24",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:e.jsx("path",{d:"M5.68964 20.3144L4.94119 20.3627L5.68964 20.3144ZM18.3104 20.3144L19.0588 20.3627V20.3627L18.3104 20.3144ZM2.75 5C2.33579 5 2 5.33579 2 5.75C2 6.16421 2.33579 6.5 2.75 6.5V5ZM21.25 6.5C21.6642 6.5 22 6.16421 22 5.75C22 5.33579 21.6642 5 21.25 5V6.5ZM10.5 10.75C10.5 10.3358 10.1642 10 9.75 10C9.33579 10 9 10.3358 9 10.75H10.5ZM9 16.25C9 16.6642 9.33579 17 9.75 17C10.1642 17 10.5 16.6642 10.5 16.25H9ZM15 10.75C15 10.3358 14.6642 10 14.25 10C13.8358 10 13.5 10.3358 13.5 10.75H15ZM13.5 16.25C13.5 16.6642 13.8358 17 14.25 17C14.6642 17 15 16.6642 15 16.25H13.5ZM15.1477 5.93694C15.2509 6.33808 15.6598 6.57957 16.0609 6.47633C16.4621 6.37308 16.7036 5.9642 16.6003 5.56306L15.1477 5.93694ZM4.00156 5.79829L4.94119 20.3627L6.43808 20.2661L5.49844 5.70171L4.00156 5.79829ZM6.68756 22H17.3124V20.5H6.68756V22ZM19.0588 20.3627L19.9984 5.79829L18.5016 5.70171L17.5619 20.2661L19.0588 20.3627ZM19.25 5H4.75V6.5H19.25V5ZM2.75 6.5H4.75V5H2.75V6.5ZM19.25 6.5H21.25V5H19.25V6.5ZM17.3124 22C18.2352 22 18.9994 21.2835 19.0588 20.3627L17.5619 20.2661C17.5534 20.3976 17.4443 20.5 17.3124 20.5V22ZM4.94119 20.3627C5.0006 21.2835 5.76481 22 6.68756 22V20.5C6.55574 20.5 6.44657 20.3976 6.43808 20.2661L4.94119 20.3627ZM9 10.75V16.25H10.5V10.75H9ZM13.5 10.75V16.25H15V10.75H13.5ZM12 3.5C13.5134 3.5 14.7868 4.53504 15.1477 5.93694L16.6003 5.56306C16.0731 3.51451 14.2144 2 12 2V3.5ZM8.85237 5.93694C9.21319 4.53504 10.4867 3.5 12 3.5V2C9.78568 2 7.92697 3.51451 7.39971 5.56306L8.85237 5.93694Z",fill:"#868C98"})})})]})})]})},t)})]}),e.jsxs("div",{className:"border-t border-gray-200 pt-4",children:[e.jsx("h3",{className:"mb-4 text-lg font-medium text-gray-900",children:"Add Custom Field"}),e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{className:"grid grid-cols-2 gap-4",children:[e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700",children:"Field Label"}),e.jsx("input",{type:"text",name:"label",value:d.label,onChange:v,placeholder:"Enter field label",className:"mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-primaryBlue focus:ring-primaryBlue sm:text-sm"})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700",children:"Field Type"}),e.jsx("select",{name:"type",value:d.type,onChange:v,className:"mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-primaryBlue focus:ring-primaryBlue sm:text-sm",children:q.map(s=>e.jsx("option",{value:s.value,children:s.label},s.value))})]})]}),d.type==="number"&&e.jsxs("div",{className:"grid grid-cols-3 gap-4",children:[e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700",children:"Min"}),e.jsx("input",{type:"number",name:"min",value:d.min,onChange:v,className:"mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-primaryBlue focus:ring-primaryBlue sm:text-sm"})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700",children:"Max"}),e.jsx("input",{type:"number",name:"max",value:d.max,onChange:v,className:"mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-primaryBlue focus:ring-primaryBlue sm:text-sm"})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700",children:"Step"}),e.jsx("input",{type:"number",name:"step",value:d.step,onChange:v,className:"mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-primaryBlue focus:ring-primaryBlue sm:text-sm"})]})]}),d.type==="select"&&e.jsxs("div",{className:"space-y-2",children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700",children:"Options"}),e.jsxs("div",{className:"flex space-x-2",children:[e.jsx("input",{type:"text",value:p,onChange:s=>M(s.target.value),placeholder:"Add option",className:"block w-full rounded-md border-gray-300 shadow-sm focus:border-primaryBlue focus:ring-primaryBlue sm:text-sm"}),e.jsx("button",{type:"button",onClick:ee,className:"inline-flex items-center rounded-md border border-transparent bg-primaryBlue px-3 py-2 text-sm font-medium text-white shadow-sm hover:bg-primaryBlue/80",children:"Add"})]}),e.jsx("div",{className:"mt-2 space-y-2",children:d.options.map((s,t)=>e.jsxs("div",{className:"flex items-center justify-between rounded bg-gray-50 p-2",children:[e.jsx("span",{children:s}),e.jsx("button",{type:"button",onClick:()=>se(s),className:"text-red-500 hover:text-red-700",children:e.jsx("svg",{width:"24",height:"24",viewBox:"0 0 24 24",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:e.jsx("path",{d:"M5.68964 20.3144L4.94119 20.3627L5.68964 20.3144ZM18.3104 20.3144L19.0588 20.3627V20.3627L18.3104 20.3144ZM2.75 5C2.33579 5 2 5.33579 2 5.75C2 6.16421 2.33579 6.5 2.75 6.5V5ZM21.25 6.5C21.6642 6.5 22 6.16421 22 5.75C22 5.33579 21.6642 5 21.25 5V6.5ZM10.5 10.75C10.5 10.3358 10.1642 10 9.75 10C9.33579 10 9 10.3358 9 10.75H10.5ZM9 16.25C9 16.6642 9.33579 17 9.75 17C10.1642 17 10.5 16.6642 10.5 16.25H9ZM15 10.75C15 10.3358 14.6642 10 14.25 10C13.8358 10 13.5 10.3358 13.5 10.75H15ZM13.5 16.25C13.5 16.6642 13.8358 17 14.25 17C14.6642 17 15 16.6642 15 16.25H13.5ZM15.1477 5.93694C15.2509 6.33808 15.6598 6.57957 16.0609 6.47633C16.4621 6.37308 16.7036 5.9642 16.6003 5.56306L15.1477 5.93694ZM4.00156 5.79829L4.94119 20.3627L6.43808 20.2661L5.49844 5.70171L4.00156 5.79829ZM6.68756 22H17.3124V20.5H6.68756V22ZM19.0588 20.3627L19.9984 5.79829L18.5016 5.70171L17.5619 20.2661L19.0588 20.3627ZM19.25 5H4.75V6.5H19.25V5ZM2.75 6.5H4.75V5H2.75V6.5ZM19.25 6.5H21.25V5H19.25V6.5ZM17.3124 22C18.2352 22 18.9994 21.2835 19.0588 20.3627L17.5619 20.2661C17.5534 20.3976 17.4443 20.5 17.3124 20.5V22ZM4.94119 20.3627C5.0006 21.2835 5.76481 22 6.68756 22V20.5C6.55574 20.5 6.44657 20.3976 6.43808 20.2661L4.94119 20.3627ZM9 10.75V16.25H10.5V10.75H9ZM13.5 10.75V16.25H15V10.75H13.5ZM12 3.5C13.5134 3.5 14.7868 4.53504 15.1477 5.93694L16.6003 5.56306C16.0731 3.51451 14.2144 2 12 2V3.5ZM8.85237 5.93694C9.21319 4.53504 10.4867 3.5 12 3.5V2C9.78568 2 7.92697 3.51451 7.39971 5.56306L8.85237 5.93694Z",fill:"#868C98"})})})]},t))})]}),e.jsx("div",{className:"flex justify-end",children:e.jsxs("button",{type:"button",onClick:te,className:"inline-flex items-center rounded-xl border border-transparent bg-primaryBlue px-4 py-2 text-sm font-medium text-white shadow-sm hover:bg-primaryBlue/80 focus:outline-none focus:ring-2 focus:ring-primaryBlue focus:ring-offset-2",children:[e.jsx(we,{className:"mr-2"}),"Add Field"]})})]})]}),e.jsx("div",{className:"pt-4",children:e.jsx(Ne,{type:"submit",loading:I,disabled:I,className:"flex w-fit justify-center rounded-xl border border-transparent bg-primaryBlue px-4 py-2 text-sm font-medium text-white shadow-sm hover:bg-primaryBlue/80 focus:outline-none focus:ring-2 focus:ring-primaryBlue focus:ring-offset-2",children:"Save Form Settings"})})]})]})}export{Pe as C};
