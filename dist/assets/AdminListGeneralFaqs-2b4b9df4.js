import{j as t}from"./@nivo/heatmap-ba1ecfff.js";import{b as a,f as K,r as u}from"./vendor-851db8c1.js";import{w as Y,M as I,T as J,G as U,A as V,c as W,E as X,R as S,t as Z,i as ee}from"./index-12adfaa3.js";import{c as te,a as l}from"./yup-54691517.js";import{u as ae}from"./react-hook-form-687afde5.js";import{o as se}from"./yup-2824f222.js";import{P as oe}from"./index-eb1bc208.js";import{_ as re}from"./lodash-91d5d207.js";import ie from"./Skeleton-1e8bf077.js";import{L as ne}from"./LoadingOverlay-87926629.js";import{D as le}from"./DataTable-a2248415.js";import{A as ce,F as me}from"./FaqDetails-bacbb84b.js";import{H as pe}from"./HistoryComponent-90e771dd.js";import"./@craftjs/core-d3c11b68.js";import"./@fortawesome/react-fontawesome-13437837.js";import"./@fortawesome/fontawesome-svg-core-4fa3e289.js";import"./react-confirm-alert-cd7ccfe7.js";import"./@tanstack/react-query-20158223.js";import"./@stripe/stripe-js-6b714a86.js";import"./moment-a9aaa855.js";import"./cal-heatmap-cf010ec4.js";import"./react-icons-51bc3cff.js";import"./smoothscroll-polyfill-a5c0a116.js";import"./date-fns-07266b7d.js";import"./numeral-ea653b2a.js";import"./@stripe/react-stripe-js-64f0e61f.js";import"./react-select-c8303602.js";import"./@mantine/core-8cbffb6d.js";import"./@emotion/react-89b506c3.js";import"./@emotion/cache-9a5b99cd.js";import"./@emotion/utils-8a8f62c5.js";import"./@emotion/serialize-460cad7f.js";import"./@uppy/dashboard-4a19149e.js";import"./@fullcalendar/core-8ccc1ac4.js";import"./@uppy/core-0760343f.js";import"./@uppy/aws-s3-c5961f7a.js";import"./@uppy/compressor-11f993e4.js";import"./@headlessui/react-a5400090.js";import"./@fortawesome/free-solid-svg-icons-0a9c4907.js";import"./@fortawesome/free-regular-svg-icons-0a88e957.js";import"./@fortawesome/free-brands-svg-icons-fae0dcac.js";import"./@hookform/resolvers-67648cca.js";import"./react-loading-skeleton-3d87d1f5.js";let de=new I,ue=new J;const fe=[{header:"Subcategory",accessor:"subcategory_id",isSorted:!1,isSortedDesc:!1,mappingExist:!1,mappings:{}},{header:"Answer",accessor:"answer",isSorted:!1,isSortedDesc:!1,mappingExist:!1,mappings:{}},{header:"Create At",accessor:"create_at",isSorted:!1,isSortedDesc:!1,mappingExist:!1,mappings:{}}],ge=()=>{const{dispatch:b,state:xe}=a.useContext(U),{dispatch:j}=a.useContext(V),[v,F]=a.useState([]),[r,f]=a.useState(10),[A,C]=a.useState(0),[he,_]=a.useState(0),[c,D]=a.useState(1),[N,q]=a.useState(!1),[P,E]=a.useState(!1),[ye,w]=a.useState(!1),[m,p]=a.useState(!0),[T,Se]=a.useState(!1),[k,be]=a.useState(!1);a.useState(),K();const g=a.useRef(null),[L,x]=a.useState(!1),[R,O]=a.useState(null),[$,d]=u.useState(!1);u.useState([]),u.useState([]);const M=te({id:l(),email:l(),role:l(),status:l()});ae({resolver:se(M)});function Q(){i(c-1,r)}function z(){i(c+1,r)}async function i(e,o,y={},n=[]){p(!(k||T));try{const s=await ue.getPaginate("faq",{page:e,limit:o,filter:[...n,"general,eq,1"],join:["faq_subcategory|subcategory_id",`${de._project_id}_faq_category|faq_subcategory.category_id,id`],size:r});s&&(p(!1),F(s.list),f(s.limit),C(s.num_pages),D(s.page),_(s.total),q(s.page>1),E(s.page+1<=s.num_pages))}catch(s){p(!1),console.log("ERROR",s),Z(j,s.message)}}a.useEffect(()=>{b({type:"SETPATH",payload:{path:"faq"}}),i(1,r,{})},[]);const h=e=>{g.current&&!g.current.contains(e.target)&&w(!1)};a.useEffect(()=>(document.addEventListener("mousedown",h),()=>{document.removeEventListener("mousedown",h)}),[]);const G=e=>{O(e),x(!0)},H={type:e=>{var o;return t.jsx("span",{className:"capitalize",children:((o=ee.find(y=>{var n;return y.value==((n=e==null?void 0:e.booking)==null?void 0:n.reservation_type)}))==null?void 0:o.label)||"--"})},question:e=>t.jsx("span",{className:"capitalize",children:(e==null?void 0:e.faq_subcategory.name)||"--"}),answer:e=>t.jsx(t.Fragment,{children:re.truncate(e==null?void 0:e.answer,{length:70})||"--"}),category:e=>t.jsx(t.Fragment,{children:(e==null?void 0:e.faq_category.name)||"--"}),subcategory_id:e=>t.jsx(t.Fragment,{children:(e==null?void 0:e.faq_subcategory.name)||"--"}),create_at:e=>{if(!(e!=null&&e.create_at))return"--";const o=new Date(e.create_at);return`${o.getDate()}, ${o.toLocaleString("default",{month:"long"})}, ${o.getFullYear()}`}},B=()=>{i(1,r)};return t.jsxs("div",{className:"h-screen px-8",children:[m&&t.jsx(ne,{}),t.jsxs("div",{className:"flex flex-col justify-between gap-4 py-3 md:flex-row md:items-center",children:[t.jsx("div",{className:"flex items-center gap-4",children:t.jsxs("form",{className:"relative flex flex-1 items-center",children:[t.jsx("div",{className:"pointer-events-none absolute inset-y-0 left-0 flex items-center pl-3",children:t.jsx(W,{className:"text-gray-500"})}),t.jsx("input",{type:"text",className:"block w-full rounded-lg border border-gray-200 py-2 pl-10 pr-12 text-sm placeholder:text-gray-500 focus:border-blue-500 focus:outline-none focus:ring-1 focus:ring-blue-500",placeholder:"Search FAQ by answer",onChange:e=>{const o=e.target.value.trim();o?i(1,r,{},[`answer,cs,${o}`]):i(1,r)}})]})}),t.jsxs("div",{className:"flex items-center gap-4",children:[t.jsxs("button",{onClick:()=>d(!0),className:"inline-flex max-w-fit items-center gap-2 rounded-lg bg-[#1D275F] px-4 py-2 text-sm font-semibold text-white hover:bg-blue-700",children:[t.jsx("span",{children:"+"}),"Add new"]}),t.jsx(pe,{activityType:X.faq,emptyMessage:"No FAQ history found",title:"FAQ History"})]})]}),t.jsx(S,{isOpen:$,onClose:()=>d(!1),title:"Add New FAQ",showFooter:!1,children:t.jsx(ce,{onClose:()=>d(!1),onSuccess:B})}),t.jsx(S,{isOpen:L,onClose:()=>x(!1),title:"FAQ Details",showFooter:!1,children:t.jsx(me,{faq:R})}),m?t.jsx(ie,{}):t.jsx("div",{className:"overflow-x-auto",children:t.jsx(le,{columns:fe,data:v,loading:m,renderCustomCell:H,rowClassName:"hover:bg-gray-40 bg-gray-100 px-4 py-3 text-gray-500 cursor-pointer",cellClassName:"whitespace-nowrap px-6 py-4",headerClassName:"px-6 py-4 text-left text-sm font-medium text-gray-500",emptyMessage:"No data available",onClick:e=>G(e)})}),t.jsx(oe,{currentPage:c,pageCount:A,pageSize:r,canPreviousPage:N,canNextPage:P,updatePageSize:e=>{f(e),i(1,e)},previousPage:Q,nextPage:z,gotoPage:e=>i(e,r)})]})},mt=Y(ge,"general_faqs","You don't have permission to access general FAQs");export{mt as default};
