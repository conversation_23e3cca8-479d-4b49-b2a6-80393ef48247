import{j as t}from"./@nivo/heatmap-ba1ecfff.js";import{k as f,f as u,r}from"./vendor-851db8c1.js";import{A as d,M as x}from"./index-12adfaa3.js";import"./@craftjs/core-d3c11b68.js";import"./@fortawesome/react-fontawesome-13437837.js";import"./@fortawesome/fontawesome-svg-core-4fa3e289.js";import"./react-confirm-alert-cd7ccfe7.js";import"./@tanstack/react-query-20158223.js";import"./@stripe/stripe-js-6b714a86.js";import"./moment-a9aaa855.js";import"./cal-heatmap-cf010ec4.js";import"./react-icons-51bc3cff.js";import"./smoothscroll-polyfill-a5c0a116.js";import"./date-fns-07266b7d.js";import"./lodash-91d5d207.js";import"./numeral-ea653b2a.js";import"./@stripe/react-stripe-js-64f0e61f.js";import"./react-hook-form-687afde5.js";import"./react-select-c8303602.js";import"./@mantine/core-8cbffb6d.js";import"./@emotion/react-89b506c3.js";import"./@emotion/cache-9a5b99cd.js";import"./@emotion/utils-8a8f62c5.js";import"./@emotion/serialize-460cad7f.js";import"./@uppy/dashboard-4a19149e.js";import"./@fullcalendar/core-8ccc1ac4.js";import"./@uppy/core-0760343f.js";import"./@uppy/aws-s3-c5961f7a.js";import"./@uppy/compressor-11f993e4.js";import"./@headlessui/react-a5400090.js";import"./@fortawesome/free-solid-svg-icons-0a9c4907.js";import"./@fortawesome/free-regular-svg-icons-0a88e957.js";import"./@fortawesome/free-brands-svg-icons-fae0dcac.js";function Y(){const[i]=f(),e=u(),[o,s]=r.useState(""),[p,m]=r.useState(!0),{dispatch:c}=r.useContext(d);return r.useEffect(()=>{(async()=>{const a=i.get("token");if(!a){s("Verification token is missing"),m(!1);return}try{const l=await new x().verifyEmail(a);c({type:"LOGIN",payload:l}),e("/coach/dashboard")}catch(n){s(n.message||"Email verification failed"),m(!1)}})()},[i,e]),p?t.jsx("div",{className:"flex min-h-screen items-center justify-center",children:t.jsx("div",{className:"text-xl",children:"Verifying your email..."})}):o?t.jsx("div",{className:"flex min-h-screen items-center justify-center",children:t.jsx("div",{className:"text-red-500",children:o})}):null}export{Y as default};
