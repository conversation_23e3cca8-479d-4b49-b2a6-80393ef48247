import { useEffect, useState } from "react";
import { IoChevronDownOutline, IoChevronForwardOutline } from "react-icons/io5";
import { BsThreeDots } from "react-icons/bs";
import { Link } from "react-router-dom";
import { GlobalContext, showToast } from "Context/Global";
import React, { useContext } from "react";
import MkdSDK from "Utils/MkdSDK";
import { AuthContext, tokenExpireError } from "Context/Auth";
import { fCurrency } from "Utils/formatNumber";
import LoadingSpinner from "Components/LoadingSpinner";
import { InteractiveButton } from "Components/InteractiveButton";
import { useClub } from "Context/Club";
import TreeSDK from "Utils/TreeSDK";

let sdk = new MkdSDK();
let tdk = new TreeSDK();

const formatTimestamp = (timestamp) => {
  const date = new Date(timestamp * 1000);
  return date.toLocaleDateString("en-US", {
    year: "numeric",
    month: "long",
    day: "numeric",
  });
};

export default function UserMembership() {
  const [expandedItems, setExpandedItems] = useState({});
  const [currentTableData, setCurrentTableData] = useState([]);
  const [pageSize, setPageSize] = useState(10);
  const [pageCount, setPageCount] = useState(1);
  const [page, setPage] = useState(1);
  const [dataTotal, setDataTotal] = useState(0);
  const [canPreviousPage, setCanPreviousPage] = useState(false);
  const [canNextPage, setCanNextPage] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [cancelSubscriptionModal, setCancelSubscriptionModal] = useState(false);
  const [familyMembers, setFamilyMembers] = useState([]);
  const [cancelSubscriptionLoading, setCancelSubscriptionLoading] =
    useState(false);
  const [selectedSubscription, setSelectedSubscription] = useState({});
  const [activeSubscription, setActiveSubscription] = useState({});
  const [selectedUserId, setSelectedUserId] = useState(null);
  const { triggerRefetch } = useClub();

  const { dispatch: globalDispatch } = useContext(GlobalContext);
  const { dispatch, state } = React.useContext(AuthContext);

  async function getData(pageNum, limitNum, data = {}) {
    setIsLoading(true);
    try {
      console.log(state);
      // Use the selectedUserId if provided, otherwise use the main user
      const targetUserId =
        selectedUserId || parseInt(localStorage.getItem("user"));
      data.user_id = targetUserId;
      const result = await sdk.getCustomerStripeSubscriptions(
        { page: pageNum, limit: limitNum },
        data
      );
      const { list, total, limit, num_pages, page } = result;

      // Initialize expanded state for active subscriptions
      const initialExpandedState = {};
      list.forEach((item) => {
        if (item.status === "active") {
          initialExpandedState[item.subId] = true;
        }
      });
      setExpandedItems(initialExpandedState);

      setCurrentTableData(list);
      setPageSize(+limit);
      setPageCount(+num_pages);
      setPage(+page);
      setDataTotal(+total);
      setCanPreviousPage(+page > 1);
      setCanNextPage(+page + 1 <= +num_pages);
    } catch (error) {
      console.error(error);
      tokenExpireError(dispatch, error.code);
    } finally {
      setIsLoading(false);
    }
  }

  const user_id = parseInt(localStorage.getItem("user"));
  async function getActiveSubscription() {
    try {
      const targetUserId = selectedUserId || user_id;
      const data = await sdk.getCustomerStripeSubscription(targetUserId);
      setActiveSubscription(data.customer);
    } catch (error) {
      console.error(error);
      tokenExpireError(dispatch, error.code);
    }
  }

  const cancelSubscription = async (subId) => {
    setCancelSubscriptionLoading(true);
    try {
      const data = await sdk.cancelStripeSubscription(subId);
      if (data.error) {
        console.error(data.message);
        showToast(globalDispatch, data.message, 7500, "error");
        return;
      }
      showToast(globalDispatch, data.message, 10000, "success");
      getData(1, pageSize);
      setCancelSubscriptionModal(false);
      setSelectedSubscription({});
    } catch (error) {
      console.error(error);
      showToast(globalDispatch, error.message, 7500, "error");
      tokenExpireError(dispatch, error.code);
    } finally {
      setCancelSubscriptionLoading(false);
    }
  };
  const fetchFamilyMembers = async () => {
    try {
      const familyResponse = await tdk.getList("user", {
        filter: [`guardian,eq,${user_id}`, `role,cs,user`],
      });
      setFamilyMembers(familyResponse.list);
    } catch (error) {
      console.error("Error fetching family members:", error);
    }
  };
  useEffect(() => {
    getData(1, pageSize);
    fetchFamilyMembers();
  }, [user_id]);

  useEffect(() => {
    getActiveSubscription();
  }, [user_id]);

  // Refetch data when selectedUserId changes
  useEffect(() => {
    if (selectedUserId !== null) {
      getData(1, pageSize);
      getActiveSubscription();
    }
  }, [selectedUserId]);

  const onCurrentUserChange = (selectedUserId) => {
    setSelectedUserId(selectedUserId);
  };
  console.log("currentTableData", currentTableData);
  return (
    <div className="mx-auto max-w-3xl p-3 sm:p-6">
      {isLoading && <LoadingSpinner />}
      <div className="mb-6 flex flex-col gap-4 sm:flex-row sm:items-center sm:justify-between">
        <h1 className="text-2xl font-semibold">Membership</h1>
        <Link
          to="/user/membership/buy"
          className="w-full rounded-lg bg-blue-600 px-4 py-2 text-center text-white transition-colors hover:bg-blue-700 sm:w-auto"
        >
          Buy new plan
        </Link>
      </div>

      <div className="mb-4 border-b border-gray-200" />

      <div className="relative mb-4 flex justify-end">
        <div className="mb-4">
          <div className="relative">
            <select
              value={selectedUserId || user_id}
              onChange={(e) => {
                const selectedId = parseInt(e.target.value);

                if (selectedId === user_id) {
                  // User selected "Myself" - reset to show main user's subscriptions
                  setSelectedUserId(null);
                } else {
                  // User selected a family member - show their subscriptions
                  onCurrentUserChange(selectedId);
                }
              }}
              className="w-full appearance-none rounded-lg border border-gray-300 bg-white px-3 py-2 pr-8 text-sm focus:border-blue-500 focus:outline-none focus:ring-1 focus:ring-blue-500"
            >
              <option value={user_id}>Myself</option>
              {familyMembers.map((member) => (
                <option key={member.id} value={member.id}>
                  {member.first_name} {member.last_name} (
                  {member.family_role || "Family Member"})
                </option>
              ))}
            </select>
          </div>
          <p className="mt-1 text-xs text-gray-500">
            Select the user whose membership you want to manage
          </p>
        </div>
      </div>

      {/* No subscriptions message */}
      {!isLoading && currentTableData.length === 0 && (
        <div className="flex flex-col items-center justify-center rounded-xl bg-gray-50 px-4 py-12 text-center">
          <div className="mb-4 rounded-full bg-blue-100 p-3">
            <svg
              className="h-6 w-6 text-blue-600"
              fill="none"
              viewBox="0 0 24 24"
              stroke="currentColor"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
              />
            </svg>
          </div>
          <h3 className="mb-2 text-lg font-medium text-gray-900">
            No Active Memberships
          </h3>
          <p className="mb-6 text-sm text-gray-600">
            You currently don't have any active membership plans.
          </p>
          <Link
            to="/user/membership/buy"
            className="rounded-lg bg-blue-600 px-4 py-2 text-white transition-colors hover:bg-blue-700"
          >
            Browse Plans
          </Link>
        </div>
      )}

      {/* Subscription Plans */}
      {currentTableData.map((item) => (
        <div key={item.subId} className="mb-4 rounded-xl bg-gray-50">
          <button
            onClick={() => {
              setExpandedItems((prev) => ({
                ...prev,
                [item.subId]: !prev[item.subId],
              }));
            }}
            className="flex w-full cursor-pointer flex-col gap-2 p-4 hover:bg-gray-100 sm:flex-row sm:items-center sm:justify-between"
          >
            <div className="flex items-center gap-2">
              {expandedItems[item.subId] ? (
                <IoChevronDownOutline size={20} />
              ) : (
                <IoChevronForwardOutline size={20} />
              )}
              <span className="text-sm font-medium sm:text-base">
                {formatTimestamp(item.currentPeriodStart)} -{" "}
                {formatTimestamp(item.currentPeriodEnd)}
              </span>
            </div>
            <div className="flex items-center justify-between gap-3 pl-7 sm:pl-0">
              <span className="text-sm text-gray-600 sm:text-base">
                {item.planName}
              </span>
              <span
                className={`rounded-full px-3 py-1 text-sm capitalize ${
                  item.status === "active"
                    ? "bg-green-100 text-green-800"
                    : "bg-red-100 text-red-800"
                }`}
              >
                {item.status}
              </span>
            </div>
          </button>

          {expandedItems[item.subId] && (
            <div className="p-4">
              <div className="space-y-6 rounded-xl border bg-white p-4">
                <div className="flex flex-col justify-between gap-1 sm:flex-row sm:items-center">
                  <span className="text-gray-600">Plan price</span>
                  <div className="text-left sm:text-right">
                    <div className="font-semibold">
                      {fCurrency(item.planAmount)}
                    </div>
                    <div className="text-sm text-gray-500">Billed annually</div>
                  </div>
                </div>

                <div className="flex flex-col justify-between gap-1 sm:flex-row">
                  <span className="text-gray-600">Purchased on</span>
                  <span className="text-sm sm:text-base">
                    {formatTimestamp(item.createdAt)}
                  </span>
                </div>

                <div className="flex flex-col justify-between gap-1 sm:flex-row">
                  <span className="text-gray-600">Valid until</span>
                  <span className="text-sm sm:text-base">
                    {formatTimestamp(item.currentPeriodEnd)}
                  </span>
                </div>

                <div className="flex flex-col justify-between gap-1 sm:flex-row">
                  <span className="text-gray-600">Subscription ID</span>
                  <span className="break-all text-sm sm:text-base">
                    {item.subId}
                  </span>
                </div>
                {item.status === "active" && (
                  <div className="flex justify-center sm:justify-start">
                    <button
                      onClick={() => {
                        setCancelSubscriptionModal(true);
                        setSelectedSubscription(item);
                      }}
                      className="w-full rounded-xl bg-red-500 px-5 py-2 text-white transition-colors hover:bg-red-600 sm:w-auto"
                    >
                      Cancel plan
                    </button>
                  </div>
                )}
              </div>
            </div>
          )}
        </div>
      ))}

      {/* cancel subscription  */}
      <div
        className={`fixed inset-0 z-50 flex items-center justify-center p-4 ${
          cancelSubscriptionModal ? "" : "hidden"
        }`}
      >
        <div className="fixed inset-0 bg-black opacity-50"></div>
        <div className="relative z-50 w-full max-w-lg rounded-3xl bg-white p-4 sm:p-6">
          <div className="flex flex-col gap-4 sm:flex-row">
            <div>
              <svg
                width="40"
                height="40"
                viewBox="0 0 40 40"
                fill="none"
                xmlns="http://www.w3.org/2000/svg"
              >
                <rect width="40" height="40" rx="10" fill="#FDEDF0" />
                <path
                  d="M20 29C15.0293 29 11 24.9707 11 20C11 15.0293 15.0293 11 20 11C24.9707 11 29 15.0293 29 20C29 24.9707 24.9707 29 20 29ZM19.1 22.7V24.5H20.9V22.7H19.1ZM19.1 15.5V20.9H20.9V15.5H19.1Z"
                  fill="#DF1C41"
                />
              </svg>
            </div>
            <div>
              <h2 className="mb-4 text-xl font-medium">Are you sure?</h2>
              <p className="mb-6 text-gray-600">
                Are you sure you want to cancel membership?
              </p>
            </div>
          </div>
          <div className="flex flex-col-reverse gap-3 border-t pt-4 sm:flex-row sm:justify-end">
            <button
              onClick={() => {
                setCancelSubscriptionModal(false);
                setSelectedSubscription({});
              }}
              className="w-full rounded-lg border border-gray-300 px-4 py-2 hover:bg-gray-50 sm:w-auto"
            >
              Go back
            </button>
            <InteractiveButton
              onClick={() => {
                cancelSubscription(selectedSubscription.subId);
                triggerRefetch();
              }}
              className="w-full rounded-lg bg-red-600 px-4 py-2 text-white hover:bg-red-700 sm:w-auto"
              loading={cancelSubscriptionLoading}
            >
              Yes, cancel
            </InteractiveButton>
          </div>
        </div>
      </div>
    </div>
  );
}
