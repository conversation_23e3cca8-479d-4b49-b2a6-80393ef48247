import React, { useState, useCallback, useEffect, useRef } from "react";
import { format } from "date-fns";
import { IoClose } from "react-icons/io5";
import { InteractiveButton } from "Components/InteractiveButton";
import { Tooltip } from "react-tooltip";

const TimeSlots = ({
  selectedDate,
  timeRange,
  onTimeClick,
  onNext,
  nextButtonText = "Next",
  startHour = 8,
  endHour = 24,
  interval = 30,
  className = "",
  multipleSlots = false,
  timeSlots = [],
  onTimeSlotsChange,
  individualSelection = false,
  isTimeSlotAvailable,
  clubTimes = [],
  isLoading,
  coachAvailability = [],
  height = "h-fit",
  minBookingTime = 30, // Minimum booking time in minutes
  enforceMinBookingTime = false, // Whether to enforce minimum booking time
}) => {
  // Track multiple time ranges
  const [timeRanges, setTimeRanges] = useState([]);
  // Track selected time slots for current selection
  const [selectedTimes, setSelectedTimes] = useState([]);
  // Track container max height
  const [maxHeight, setMaxHeight] = useState(350);
  const timeSlotsContainerRef = useRef(null);
  const generateTimeSlots = useCallback(() => {
    const slots = [];
    for (let hour = startHour; hour <= endHour; hour++) {
      for (let minute = 0; minute < 60; minute += interval) {
        const actualHour = hour === 24 ? 0 : hour;
        const time24 = `${actualHour.toString().padStart(2, "0")}:${minute
          .toString()
          .padStart(2, "0")}`;
        const period = actualHour >= 12 ? "PM" : "AM";
        const hour12 =
          actualHour === 0
            ? 12
            : actualHour > 12
            ? actualHour - 12
            : actualHour;
        const time12 = `${hour12}:${minute
          .toString()
          .padStart(2, "0")} ${period}`;
        slots.push({ time24, time12 });
      }
    }
    return slots;
  }, [startHour, endHour, interval]);
  // Sync internal selectedTimes state with timeRange prop when it changes
  useEffect(() => {
    if (
      timeRange &&
      timeRange.length > 0 &&
      timeRange[0].from &&
      timeRange[0].until
    ) {
      const timeSlotsList = generateTimeSlots();
      const fromTime = timeRange[0].from;
      const untilTime = timeRange[0].until;

      // Find the start time slot
      const startSlot = timeSlotsList.find((slot) => slot.time12 === fromTime);
      if (!startSlot) return;

      // Find the end time slot (the slot before the "until" time)
      const untilSlotIndex = timeSlotsList.findIndex(
        (slot) => slot.time12 === untilTime
      );
      if (untilSlotIndex === -1) return;

      // Generate all time slots between start and end (excluding the "until" time)
      const startSlotIndex = timeSlotsList.findIndex(
        (slot) => slot.time12 === fromTime
      );
      const selectedTimeSlots = [];

      for (let i = startSlotIndex; i < untilSlotIndex; i++) {
        selectedTimeSlots.push(timeSlotsList[i].time24);
      }

      setSelectedTimes(selectedTimeSlots);
    } else {
      // Clear selection if timeRange is empty
      setSelectedTimes([]);
    }
  }, [timeRange, generateTimeSlots]);

  // Adjust max height based on screen size
  useEffect(() => {
    const updateMaxHeight = () => {
      const viewportHeight = window.innerHeight;
      // Calculate appropriate height based on viewport
      // Smaller screens get smaller containers to ensure button visibility
      if (viewportHeight < 600) {
        setMaxHeight(200);
      } else if (viewportHeight < 800) {
        setMaxHeight(300);
      } else {
        setMaxHeight(350);
      }
    };

    // Set initial height
    updateMaxHeight();

    // Update on resize
    window.addEventListener("resize", updateMaxHeight);
    return () => window.removeEventListener("resize", updateMaxHeight);
  }, []);

  const isTimeInClubHours = (time24) => {
    if (!clubTimes || clubTimes.length === 0) return true;

    const [hours, minutes] = time24.split(":");
    const timeInMinutes = parseInt(hours) * 60 + parseInt(minutes);

    return clubTimes.some((slot) => {
      const [fromHours, fromMinutes] = slot.from.split(":");
      const [untilHours, untilMinutes] = slot.until.split(":");

      const fromInMinutes = parseInt(fromHours) * 60 + parseInt(fromMinutes);
      const untilInMinutes = parseInt(untilHours) * 60 + parseInt(untilMinutes);

      return timeInMinutes >= fromInMinutes && timeInMinutes <= untilInMinutes;
    });
  };

  const isTimeInExistingRange = (time24) => {
    return timeRanges.some((range) => {
      const timeSlotsList = generateTimeSlots();
      const startIndex = timeSlotsList.findIndex(
        (t) => t.time12 === range.from
      );
      const endIndex = timeSlotsList.findIndex((t) => t.time12 === range.until);
      const currentIndex = timeSlotsList.findIndex((t) => t.time24 === time24);
      return currentIndex >= startIndex && currentIndex <= endIndex;
    });
  };

  const isTimeInCoachAvailability = (time24) => {
    if (!selectedDate || !coachAvailability || coachAvailability.length === 0)
      return true;

    const dayName = selectedDate
      .toLocaleDateString("en-US", { weekday: "long" })
      .toLowerCase();
    const dayAvailability = coachAvailability.find(
      (avail) => avail.day === dayName
    );

    if (!dayAvailability) return false;

    // Convert time24 format (HH:mm) to match the availability format (HH:mm:00)
    const timeToCheck = `${time24}:00`;
    return dayAvailability.timeslots.includes(timeToCheck);
  };

  // Helper function to check if a time slot meets minimum booking requirements
  const meetsMinimumBookingTime = (startTime24, endTime24) => {
    if (!enforceMinBookingTime) return true;

    const [startHours, startMinutes] = startTime24.split(":").map(Number);
    const [endHours, endMinutes] = endTime24.split(":").map(Number);

    const startTotalMinutes = startHours * 60 + startMinutes;
    const endTotalMinutes = endHours * 60 + endMinutes;

    const duration = endTotalMinutes - startTotalMinutes;
    return duration >= minBookingTime;
  };

  const handleTimeClick = (timeObj) => {
    if (
      !isTimeInCoachAvailability(timeObj.time24) ||
      !isTimeInClubHours(timeObj.time24) ||
      (multipleSlots && isTimeInExistingRange(timeObj.time24))
    )
      return;

    const timeSlotsList = generateTimeSlots();
    const currentIndex = timeSlotsList.findIndex(
      (slot) => slot.time24 === timeObj.time24
    );

    // Check if the time is already selected
    if (selectedTimes.includes(timeObj.time24)) {
      // Only allow deselection if it's the first or last time and there are 2 or more selections
      if (selectedTimes.length >= 2) {
        const sortedTimes = [...selectedTimes].sort();
        if (
          timeObj.time24 === sortedTimes[0] ||
          timeObj.time24 === sortedTimes[sortedTimes.length - 1]
        ) {
          const newSelectedTimes = selectedTimes.filter(
            (time) => time !== timeObj.time24
          );
          setSelectedTimes(newSelectedTimes);

          // Update parent component with new time range
          const updatedSortedTimes = [...newSelectedTimes].sort();
          const startTime = timeSlotsList.find(
            (t) => t.time24 === updatedSortedTimes[0]
          )?.time12;

          // For the end time, we need to calculate the end of the time slot
          // The end time should always be 30 minutes after the last selected time
          let endTime;
          // Find the index of the last selected time
          const lastTimeIndex = timeSlotsList.findIndex(
            (t) =>
              t.time24 === updatedSortedTimes[updatedSortedTimes.length - 1]
          );
          // Get the next slot (which is 30 minutes after the last selected time)
          const nextSlot = timeSlotsList[lastTimeIndex + 1];
          endTime =
            nextSlot?.time12 ||
            timeSlotsList.find(
              (t) =>
                t.time24 === updatedSortedTimes[updatedSortedTimes.length - 1]
            )?.time12; // Fallback to last time if no next slot

          const timeRange = {
            from: startTime,
            until: endTime,
          };

          onTimeClick(timeRange);
        }
      } else if (selectedTimes.length === 1) {
        // If only one time is selected, allow deselection
        setSelectedTimes([]);
        onTimeClick({ from: "", until: "" });
      }
      return;
    }

    let newSelectedTimes;

    if (selectedTimes.length === 0) {
      // First selection - just select this time slot (no automatic second slot)
      newSelectedTimes = [timeObj.time24];
      setSelectedTimes(newSelectedTimes);
    } else {
      const sortedSelectedTimes = [...selectedTimes].sort();
      const lastSelectedTime =
        sortedSelectedTimes[sortedSelectedTimes.length - 1];
      const lastSelectedIndex = timeSlotsList.findIndex(
        (slot) => slot.time24 === lastSelectedTime
      );

      if (Math.abs(currentIndex - lastSelectedIndex) === 1) {
        // Consecutive selection
        newSelectedTimes = [...selectedTimes, timeObj.time24];
        setSelectedTimes(newSelectedTimes);
      } else {
        // Non-consecutive selection - start new sequence with just this time slot
        newSelectedTimes = [timeObj.time24];
        setSelectedTimes(newSelectedTimes);
      }
    }

    // Update parent component with selected time range using the correct newSelectedTimes
    const sortedTimes = [...newSelectedTimes].sort();

    const startTime = timeSlotsList.find(
      (t) => t.time24 === sortedTimes[0]
    )?.time12;

    // For the end time, we need to calculate the end of the time slot
    // The end time should always be 30 minutes after the last selected time
    let endTime;
    // Find the index of the last selected time
    const lastTimeIndex = timeSlotsList.findIndex(
      (t) => t.time24 === sortedTimes[sortedTimes.length - 1]
    );
    // Get the next slot (which is 30 minutes after the last selected time)
    const nextSlot = timeSlotsList[lastTimeIndex + 1];
    endTime =
      nextSlot?.time12 ||
      timeSlotsList.find(
        (t) => t.time24 === sortedTimes[sortedTimes.length - 1]
      )?.time12; // Fallback to last time if no next slot

    const timeRange = {
      from: startTime,
      until: endTime,
    };

    onTimeClick(timeRange);
  };

  const handleAddTimeRange = () => {
    if (selectedTimes.length === 0) return;

    const timeSlotsList = generateTimeSlots();
    const sortedTimes = [...selectedTimes].sort();
    const startTime = timeSlotsList.find(
      (t) => t.time24 === sortedTimes[0]
    )?.time12;

    // For the end time, we need to calculate the end of the time slot
    // The end time should always be 30 minutes after the last selected time
    let endTime;
    // Find the index of the last selected time
    const lastTimeIndex = timeSlotsList.findIndex(
      (t) => t.time24 === sortedTimes[sortedTimes.length - 1]
    );
    // Get the next slot (which is 30 minutes after the last selected time)
    const nextSlot = timeSlotsList[lastTimeIndex + 1];
    endTime =
      nextSlot?.time12 ||
      timeSlotsList.find(
        (t) => t.time24 === sortedTimes[sortedTimes.length - 1]
      )?.time12; // Fallback to last time if no next slot

    const newRange = {
      from: startTime,
      until: endTime,
    };

    const newTimeRanges = [...timeRanges, newRange];
    setTimeRanges(newTimeRanges);
    setSelectedTimes([]); // Clear current selection
    onTimeSlotsChange?.(newTimeRanges);
  };

  const removeTimeRange = (index) => {
    const newTimeRanges = timeRanges.filter((_, i) => i !== index);
    setTimeRanges(newTimeRanges);
    onTimeSlotsChange?.(newTimeRanges);
  };

  const isTimeSelected = (time24) => {
    return selectedTimes.includes(time24);
  };

  const timeSlotsList = generateTimeSlots();

  return (
    <div className={`rounded-lg bg-white p-4 shadow-5 ${className} ${height}`}>
      {selectedDate && (
        <p className="text-center font-medium">
          {format(selectedDate, "EEEE, MMMM d, yyyy")}
        </p>
      )}

      {enforceMinBookingTime && (
        <div className="mb-3 mt-2 rounded-lg bg-blue-50 p-2 text-xs text-blue-700">
          Minimum booking time: {minBookingTime} minutes
        </div>
      )}

      <div
        ref={timeSlotsContainerRef}
        className="scrollbar-hide mb-5 mt-2 flex h-full flex-col gap-2 overflow-y-auto"
        style={{ maxHeight: `${maxHeight}px` }}
      >
        {timeSlotsList.map((timeObj, index) => {
          const isInClubHours = isTimeInClubHours(timeObj.time24);
          const isCoachAvailable = isTimeInCoachAvailability(timeObj.time24);
          const isInExistingRange =
            multipleSlots && isTimeInExistingRange(timeObj.time24);
          const isAvailable =
            isCoachAvailable && isInClubHours && !isInExistingRange;

          const tooltipContent = !isInClubHours
            ? "Club Closed"
            : !isCoachAvailable
            ? "Coach not available"
            : "";

          return (
            <button
              key={timeObj.time24}
              onClick={() => handleTimeClick(timeObj)}
              disabled={!isAvailable}
              data-tooltip-id={`time-${index}`}
              data-tooltip-content={tooltipContent}
              type="button"
              className={`
                rounded-lg border-[1.5px] px-4 py-2 text-sm font-medium transition-colors
                ${
                  !isAvailable
                    ? "cursor-not-allowed border-gray-200 bg-gray-100 text-gray-400"
                    : isTimeSelected(timeObj.time24)
                    ? "border-[1.5px] border-primaryBlue bg-primaryBlue/10 text-primaryBlue"
                    : "border-gray-200 text-gray-500 hover:bg-gray-50"
                }
              `}
            >
              {timeObj.time12}
              {!isAvailable && (
                <Tooltip
                  id={`time-${index}`}
                  place="top"
                  className="z-50 !bg-gray-900 !px-2 !py-1 !text-xs !text-white"
                />
              )}
            </button>
          );
        })}
      </div>

      {/* Current Selection Display */}
      {selectedTimes.length > 0 && (
        <div className="space-y-2 border-t border-gray-200 pt-4">
          <div className="flex items-center justify-center gap-2 text-sm">
            <span className="font-medium">From: </span>
            <span className="text-primaryBlue">
              {
                timeSlotsList.find((t) => t.time24 === selectedTimes.sort()[0])
                  ?.time12
              }
            </span>
            <span className="font-medium">Until: </span>
            <span className="text-primaryBlue">
              {(() => {
                const sortedTimes = [...selectedTimes].sort();
                // Find the index of the last selected time
                const lastTimeIndex = timeSlotsList.findIndex(
                  (t) => t.time24 === sortedTimes[sortedTimes.length - 1]
                );
                // Get the next slot (which is 30 minutes after the last selected time)
                const nextSlot = timeSlotsList[lastTimeIndex + 1];
                return (
                  nextSlot?.time12 ||
                  timeSlotsList.find(
                    (t) => t.time24 === sortedTimes[sortedTimes.length - 1]
                  )?.time12
                ); // Fallback to last time if no next slot
              })()}
            </span>
          </div>
          {multipleSlots && (
            <InteractiveButton
              className="mt-2 w-full rounded-lg border border-primaryBlue bg-primaryBlue/10 px-4 py-2 text-primaryBlue hover:bg-primaryBlue/20"
              onClick={handleAddTimeRange}
              disabled={selectedTimes.length === 0}
            >
              Add Time Range
            </InteractiveButton>
          )}
        </div>
      )}

      {/* Selected Time Ranges Display */}
      {multipleSlots && timeRanges.length > 0 && (
        <div className="mt-4 space-y-2 border-t border-gray-200 pt-4">
          <p className="text-center font-medium">Selected Time Ranges</p>
          <div className="flex flex-col justify-center gap-2 ">
            {timeRanges.map((range, index) => (
              <div
                key={index}
                className="grid grid-cols-[auto_auto_auto_auto_auto] items-center gap-2 rounded-lg px-3 py-1 text-sm"
              >
                <span className="text-gray-500">From</span>
                <span>{range.from}</span>
                <span>-</span>
                <span className="text-gray-500">Until</span>
                <div className="flex items-center gap-2">
                  <span>{range.until}</span>
                  <button
                    onClick={() => removeTimeRange(index)}
                    className="text-primaryBlue hover:text-primaryBlue/80"
                  >
                    <svg
                      width="24"
                      height="24"
                      viewBox="0 0 24 24"
                      fill="none"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <path
                        d="M15 9L9 15M15 15L9 9M21.25 12C21.25 17.1086 17.1086 21.25 12 21.25C6.89137 21.25 2.75 17.1086 2.75 12C2.75 6.89137 6.89137 2.75 12 2.75C17.1086 2.75 21.25 6.89137 21.25 12Z"
                        stroke="#868C98"
                        stroke-width="1.5"
                        stroke-linecap="round"
                      />
                    </svg>
                  </button>
                </div>
              </div>
            ))}
          </div>
        </div>
      )}

      {onNext && (
        <div className="sticky bottom-0 bg-white pt-2">
          <InteractiveButton
            className="mt-2 w-full rounded-lg bg-primaryBlue px-4 py-2 text-white disabled:opacity-50"
            onClick={onNext}
            disabled={
              multipleSlots
                ? timeRanges.length === 0
                : selectedTimes.length === 0
            }
            loading={isLoading}
          >
            {nextButtonText}
          </InteractiveButton>
        </div>
      )}
    </div>
  );
};

export default TimeSlots;
