import RightSideModal from "../RightSideModal";
import { convertTo12Hour } from "Utils/utils";
import { useNavigate } from "react-router";
import { useState, useEffect, useRef } from "react";

export default function ClinicDetailsModal({
  isOpen,
  onClose,
  clinic,
  fetchCoachesForClinic,
}) {
  const navigate = useNavigate();
  const [isNotificationsEnabled, setIsNotificationsEnabled] = useState(false);
  const [coaches, setCoaches] = useState([]);
  const [loadingCoaches, setLoadingCoaches] = useState(false);
  const lastFetchedClinicId = useRef(null);
  const lastFetchedCoachIds = useRef(null);

  const handleNotificationToggle = () => {
    setIsNotificationsEnabled(!isNotificationsEnabled);
    // Here you can add API call to handle notification subscription
  };

  // Fetch coaches when modal opens and clinic has coach_ids
  useEffect(() => {
    const fetchCoaches = async () => {
      if (
        isOpen &&
        clinic &&
        clinic.coach_ids &&
        Array.isArray(clinic.coach_ids) &&
        clinic.coach_ids.length > 0 &&
        fetchCoachesForClinic
      ) {
        // Check if we've already fetched coaches for this clinic
        const coachIdsString = JSON.stringify([...clinic.coach_ids].sort());
        if (
          lastFetchedClinicId.current === clinic.id &&
          lastFetchedCoachIds.current === coachIdsString
        ) {
          return; // Already fetched coaches for this clinic
        }

        setLoadingCoaches(true);
        try {
          const coachData = await fetchCoachesForClinic(clinic.coach_ids);
          setCoaches(coachData);
          // Update refs to track what we've fetched
          lastFetchedClinicId.current = clinic.id;
          lastFetchedCoachIds.current = coachIdsString;
        } catch (error) {
          console.error("Error fetching coaches:", error);
          setCoaches([]);
        } finally {
          setLoadingCoaches(false);
        }
      } else if (!isOpen) {
        // Reset coaches when modal closes
        setCoaches([]);
        setLoadingCoaches(false);
        lastFetchedClinicId.current = null;
        lastFetchedCoachIds.current = null;
      }
    };

    fetchCoaches();
  }, [
    isOpen,
    clinic?.id,
    JSON.stringify(clinic?.coach_ids),
    fetchCoachesForClinic,
  ]);

  if (!clinic) return null;

  return (
    <RightSideModal
      isOpen={isOpen}
      onClose={onClose}
      title={clinic.name}
      showFooter={false}
      primaryButtonText="Join"
      showOnlyPrimary={true}
      className={"!p-0"}
    >
      <div className="flex h-full flex-col pb-4">
        <div className="flex flex-1 flex-col gap-4 p-5 pb-6">
          {/* Slots Available */}
          <div className="inline-flex items-center gap-2">
            {clinic.slots_remaining > 0 ? (
              <span className="rounded-full border border-[#176448] bg-green-50 px-3 py-1 text-sm text-[#176448]">
                Slots available: {clinic.slots_remaining} (out of{" "}
                {clinic.max_participants})
              </span>
            ) : (
              <span className="rounded-full border border-red-800 bg-red-50 px-3 py-1 text-sm text-red-800">
                No slots available
              </span>
            )}
          </div>

          {/* Date & Time */}
          <div className="border-1 space-y-1 rounded-xl border border-gray-200 bg-gray-100 p-4">
            <h3 className="text-sm font-medium text-gray-500">DATE & TIME</h3>
            <p className="text-base">
              {new Date(clinic.clinic_date).toLocaleDateString("en-US", {
                month: "long",
                day: "numeric",
              })}{" "}
              • {convertTo12Hour(clinic.clinic_start_time)} -{" "}
              {convertTo12Hour(clinic.clinic_end_time)}
            </p>
          </div>

          {/* Details */}
          <div className="space-y-1">
            <h3 className="text-sm font-medium text-gray-500">DETAILS</h3>
            <p className="text-base">{clinic.details}</p>
          </div>

          <div className="border border-b" />

          {/* Coaches */}
          <div className="space-y-3">
            <h3 className="text-sm font-medium text-gray-500">COACHES</h3>
            <div className="space-y-3">
              {loadingCoaches ? (
                <div className="flex items-center gap-3">
                  <div className="h-10 w-10 animate-pulse rounded-full bg-gray-200"></div>
                  <div className="space-y-2">
                    <div className="h-4 w-24 animate-pulse rounded bg-gray-200"></div>
                    <div className="h-3 w-32 animate-pulse rounded bg-gray-200"></div>
                  </div>
                </div>
              ) : coaches && coaches.length > 0 ? (
                coaches.map((coach) => (
                  <div key={coach.id} className="flex items-center gap-3">
                    <img
                      src={coach.user?.photo || "/default-avatar.png"}
                      alt={`${coach.user?.first_name} ${coach.user?.last_name}`}
                      className="h-10 w-10 rounded-full object-cover"
                    />
                    <div>
                      <p className="text-base font-medium">
                        {coach.user?.first_name} {coach.user?.last_name}
                      </p>
                      {coach.user?.email && (
                        <p className="text-sm text-gray-500">
                          {coach.user.email}
                        </p>
                      )}
                      {coach.bio && (
                        <p className="mt-1 line-clamp-2 text-xs text-gray-400">
                          {coach.bio}
                        </p>
                      )}
                    </div>
                  </div>
                ))
              ) : (
                <p className="text-base text-gray-500">No coaches assigned</p>
              )}
            </div>
          </div>
          <div className="border border-b" />

          {/* Sport & Type */}
          <div className="space-y-1">
            <h3 className="text-sm font-medium text-gray-500">SPORT & TYPE</h3>
            <p className="text-base">
              Tennis • Indoors •{" "}
              {clinic.surface_id === 1 ? "Hard Court" : "Clay Court"}
            </p>
          </div>
          <div className="border border-b" />

          {/* NTRP */}
          <div className="space-y-1">
            <h3 className="text-sm font-medium text-gray-500">NTRP</h3>
            <p className="text-base">4.0-5.0</p>
          </div>
        </div>

        {/* Join Button */}
        <div className="sticky bottom-0 border-t border-gray-200 bg-gray-100 p-4">
          {clinic.slots_remaining > 0 ? (
            <button
              onClick={() => {
                navigate(`/user/clinic-booking/${clinic.id}`, {
                  state: { clinic },
                });
              }}
              className="flex w-full items-center justify-center gap-2 rounded-xl bg-primaryBlue py-3 text-center text-white hover:bg-blue-900"
            >
              <span>
                <svg
                  width="20"
                  height="20"
                  viewBox="0 0 20 20"
                  fill="none"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    d="M10.75 11.5H9.25C8.0197 11.4995 6.81267 11.8354 5.75941 12.4712C4.70614 13.107 3.8467 14.0186 3.274 15.1075C3.2579 14.9054 3.2499 14.7027 3.25 14.5C3.25 10.3578 6.60775 7 10.75 7V2.875L18.625 9.25L10.75 15.625V11.5ZM9.25 10H12.25V12.481L16.2408 9.25L12.25 6.019V8.5H10.75C9.88769 8.49903 9.03535 8.68436 8.25129 9.04332C7.46724 9.40227 6.76999 9.92637 6.20725 10.5797C7.17574 10.1959 8.20822 9.99919 9.25 10Z"
                    fill="white"
                  />
                </svg>
              </span>
              <span> Join</span>
            </button>
          ) : (
            <div className="rounded-2xl border border-gray-200 bg-white p-4 shadow-sm">
              <div className="flex items-center justify-between">
                <div>
                  <div className="mb-2 flex items-center justify-between">
                    <h3 className="text-base font-medium text-gray-900">
                      Get notifed
                    </h3>

                    <div className="flex items-center">
                      <button
                        type="button"
                        className={`relative inline-flex h-6 w-11 flex-shrink-0 cursor-pointer rounded-full border-2 border-transparent transition-colors duration-200 ease-in-out focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 ${
                          isNotificationsEnabled ? "bg-blue-600" : "bg-gray-200"
                        }`}
                        role="switch"
                        aria-checked={isNotificationsEnabled}
                        onClick={handleNotificationToggle}
                      >
                        <span
                          aria-hidden="true"
                          className={`pointer-events-none inline-block h-5 w-5 transform rounded-full bg-white shadow ring-0 transition duration-200 ease-in-out ${
                            isNotificationsEnabled
                              ? "translate-x-5"
                              : "translate-x-0"
                          }`}
                        />
                      </button>
                    </div>
                  </div>
                  <p className="text-sm text-gray-500">
                    We will email you when slots for this clinic become
                    available again, e.g. if someone opts-put.
                  </p>
                </div>
              </div>
            </div>
          )}
        </div>
      </div>
    </RightSideModal>
  );
}
