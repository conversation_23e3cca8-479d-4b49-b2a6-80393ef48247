import { Switch } from "@headlessui/react";
import React, { useState } from "react";
import { InteractiveButton } from "./InteractiveButton";
import { useClub } from "Context/Club";

export default function PlanDetailsModal({
  onSubmit,
  onClose,
  initialData,
  mode,
  onDelete,
}) {
  const { sports } = useClub();
  const [isEditing, setIsEditing] = useState({
    name: mode === "create",
    price: mode === "create",
    features: new Set(),
  });
  console.log("currentMode", mode);
  const [isSubmitting, setIsSubmitting] = useState(false);
  // Ensure initialData has advance_booking_days property and enabled flags
  const initialPlanData = initialData
    ? {
        ...initialData,
        advance_booking_days: initialData.advance_booking_days || {
          court: 10,
          lesson: 10,
          clinic: 10,
          buddy: 10,
        },
        advance_booking_enabled: initialData.advance_booking_enabled || {
          court: true,
          lesson: true,
          clinic: true,
          buddy: true,
        },
        applicable_sports: initialData.applicable_sports || [],
      }
    : {
        plan_id: null,
        plan_name: "",
        price: 0,
        allow_clinic: false,
        allow_buddy: false,
        allow_coach: false,
        allow_groups: false,
        allow_court: false,
        features: [],
        advance_booking_days: {
          court: 10,
          lesson: 10,
          clinic: 10,
          buddy: 10,
        },
        advance_booking_enabled: {
          court: true,
          lesson: true,
          clinic: true,
          buddy: true,
        },
        applicable_sports: [],
      };

  const [planData, setPlanData] = useState(initialPlanData);

  const handleSave = async () => {
    setIsSubmitting(true);
    // Ensure advance_booking_enabled values are properly sent as booleans
    const dataToSubmit = {
      ...planData,
      advance_booking_enabled: {
        court: Boolean(planData.advance_booking_enabled.court),
        lesson: Boolean(planData.advance_booking_enabled.lesson),
        clinic: Boolean(planData.advance_booking_enabled.clinic),
        buddy: Boolean(planData.advance_booking_enabled.buddy),
      },
    };
    console.log("Submitting plan data:", dataToSubmit);
    await onSubmit(dataToSubmit, mode);
    setIsSubmitting(false);
    // onClose();
  };

  const handleFeatureEdit = (id, newText) => {
    setPlanData((prev) => ({
      ...prev,
      features: prev.features.map((feature) =>
        feature.id === id ? { ...feature, text: newText } : feature
      ),
    }));
    setIsEditing((prev) => {
      const newSet = new Set(prev.features);
      newSet.delete(id);
      return { ...prev, features: newSet };
    });
  };

  const handleFeatureDelete = (id) => {
    setPlanData((prev) => ({
      ...prev,
      features: prev.features.filter((feature) => feature.id !== id),
    }));
  };

  const handleAddFeature = () => {
    const newId = Math.max(...planData.features.map((f) => f.id), 0) + 1;
    setPlanData((prev) => ({
      ...prev,
      features: [...prev.features, { id: newId, text: "" }],
    }));
    setIsEditing((prev) => ({
      ...prev,
      features: new Set([...prev.features, newId]),
    }));
  };

  const moduleLabels = {
    allow_court: "Court booking",
    allow_clinic: "Clinics",
    allow_coach: "Lesson",
    allow_buddy: "Find a Buddy",
    allow_groups: "My Groups",
  };

  const handleEdit = (field, value) => {
    setPlanData((prev) => ({ ...prev, [field]: value }));
    setIsEditing((prev) => ({ ...prev, [field]: false }));
  };

  const handleModuleToggle = (key) => {
    setPlanData((prev) => ({
      ...prev,
      [key]: !prev[key],
    }));
  };

  const handleSportToggle = (sportId) => {
    setPlanData((prev) => {
      const currentSports = prev.applicable_sports || [];
      const isSelected = currentSports.includes(sportId);

      if (isSelected) {
        // Remove sport from selection
        return {
          ...prev,
          applicable_sports: currentSports.filter((id) => id !== sportId),
        };
      } else {
        // Add sport to selection
        return {
          ...prev,
          applicable_sports: [...currentSports, sportId],
        };
      }
    });
  };

  const handleSelectAllSports = () => {
    const activeSports = sports?.filter((sport) => sport.status === 1) || [];
    const allSportIds = activeSports.map((sport) => sport.id);
    setPlanData((prev) => ({
      ...prev,
      applicable_sports: allSportIds,
    }));
  };

  const handleDeselectAllSports = () => {
    setPlanData((prev) => ({
      ...prev,
      applicable_sports: [],
    }));
  };

  return (
    <div className="flex h-full flex-col gap-4">
      <div className="flex-1 space-y-4">
        {/* Plan Name Section */}
        <div className="space-y-2">
          <div className="flex items-center justify-between">
            <div className="text-sm text-gray-600">Plan name</div>
            <button
              className="text-sm text-blue-600"
              onClick={() => setIsEditing((prev) => ({ ...prev, name: true }))}
            >
              Edit
            </button>
          </div>
          {isEditing.name ? (
            <input
              type="text"
              value={planData.plan_name}
              onChange={(e) =>
                setPlanData((prev) => ({ ...prev, plan_name: e.target.value }))
              }
              onBlur={() => handleEdit("plan_name", planData.plan_name)}
              className="w-full rounded-md border border-gray-300 px-3 py-2"
              autoFocus
            />
          ) : (
            <div>{planData?.plan_name}</div>
          )}
        </div>

        {/* Price Section */}
        <div className="space-y-2">
          <div className="flex items-center justify-between">
            <div className="text-sm text-gray-600">Price</div>
            <button
              className="text-sm text-blue-600"
              onClick={() => setIsEditing((prev) => ({ ...prev, price: true }))}
            >
              Edit
            </button>
          </div>
          {isEditing.price ? (
            <input
              type="number"
              value={planData.price}
              onChange={(e) =>
                setPlanData((prev) => ({
                  ...prev,
                  price: parseFloat(e.target.value),
                }))
              }
              onBlur={() => handleEdit("price", planData.price)}
              className="w-full rounded-md border border-gray-300 px-3 py-2"
              step="0.01"
              autoFocus
            />
          ) : (
            <div>
              {planData.price === 0 ? (
                <span className="font-semibold text-green-600">Free</span>
              ) : (
                `$${planData.price?.toFixed(2)}`
              )}
            </div>
          )}
        </div>

        {/* Modules Section */}
        <div className="space-y-4 pt-4">
          <div className="text-sm text-gray-600">Module</div>
          <div className="space-y-4">
            {Object.entries(moduleLabels).map(([key, label]) => (
              <div key={key} className="flex items-center justify-between">
                <div>{label}</div>
                <Switch
                  checked={planData[key]}
                  onChange={() => handleModuleToggle(key)}
                  className={`${
                    planData[key] ? "bg-blue-500" : "bg-gray-200"
                  } relative inline-flex h-6 w-11 items-center rounded-full transition-colors`}
                >
                  <span
                    className={`${
                      planData[key] ? "translate-x-6" : "translate-x-1"
                    } inline-block h-4 w-4 transform rounded-full bg-white transition`}
                  />
                </Switch>
              </div>
            ))}
          </div>
        </div>

        {/* Sports Selection Section */}
        <div className="space-y-4 pt-4">
          <div className="flex items-center justify-between">
            <div className="text-sm text-gray-600">Applicable Sports</div>
            <div className="flex gap-2">
              <button
                type="button"
                onClick={handleSelectAllSports}
                className="text-xs text-blue-600 hover:text-blue-800"
              >
                Select All
              </button>
              <span className="text-xs text-gray-400">|</span>
              <button
                type="button"
                onClick={handleDeselectAllSports}
                className="text-xs text-blue-600 hover:text-blue-800"
              >
                Clear All
              </button>
            </div>
          </div>
          <div className="space-y-3">
            {sports?.filter((sport) => sport.status === 1).length > 0 ? (
              sports
                .filter((sport) => sport.status === 1)
                .map((sport) => (
                  <div
                    key={sport.id}
                    className="flex items-center justify-between"
                  >
                    <div className="flex items-center gap-3">
                      <input
                        type="checkbox"
                        id={`sport-${sport.id}`}
                        checked={
                          planData.applicable_sports?.includes(sport.id) ||
                          false
                        }
                        onChange={() => handleSportToggle(sport.id)}
                        className="h-4 w-4 rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                      />
                      <label
                        htmlFor={`sport-${sport.id}`}
                        className="cursor-pointer text-sm font-medium text-gray-700"
                      >
                        {sport.name}
                      </label>
                    </div>
                  </div>
                ))
            ) : (
              <div className="text-sm italic text-gray-500">
                No active sports available. Please add sports in your club
                settings first.
              </div>
            )}
          </div>
          {planData.applicable_sports?.length === 0 && (
            <div className="rounded-md bg-amber-50 p-2 text-xs text-amber-600">
              ⚠️ No sports selected. This membership will not apply to any
              specific sports.
            </div>
          )}
        </div>

        {/* Advance Booking Days Section */}
        <div className="space-y-4 pt-4">
          <div className="text-sm text-gray-600">Advance Booking Days</div>
          <div className="space-y-4">
            <div className="space-y-2">
              <div className="flex items-center justify-between">
                <div>Court Reservation</div>
                <Switch
                  checked={planData.advance_booking_enabled.court}
                  onChange={() => {
                    const newValue = !planData.advance_booking_enabled.court;
                    console.log("Toggling court booking enabled:", newValue);
                    setPlanData((prev) => ({
                      ...prev,
                      advance_booking_enabled: {
                        ...prev.advance_booking_enabled,
                        court: newValue,
                      },
                    }));
                  }}
                  className={`${
                    planData.advance_booking_enabled.court
                      ? "bg-blue-500"
                      : "bg-gray-200"
                  } relative mr-2 inline-flex h-6 w-11 items-center rounded-full transition-colors`}
                >
                  <span
                    className={`${
                      planData.advance_booking_enabled.court
                        ? "translate-x-6"
                        : "translate-x-1"
                    } inline-block h-4 w-4 transform rounded-full bg-white transition`}
                  />
                </Switch>
              </div>
              {planData.advance_booking_enabled.court && (
                <div className="flex items-center justify-between pl-4">
                  <div className="text-sm text-gray-500">Days in advance:</div>
                  <input
                    type="number"
                    min="1"
                    max="365"
                    value={planData.advance_booking_days.court}
                    onChange={(e) =>
                      setPlanData((prev) => ({
                        ...prev,
                        advance_booking_days: {
                          ...prev.advance_booking_days,
                          court: parseInt(e.target.value) || 1,
                        },
                      }))
                    }
                    className="w-20 rounded-md border border-gray-300 px-3 py-2 text-sm focus:border-blue-500 focus:outline-none focus:ring-1 focus:ring-blue-500"
                    required
                  />
                </div>
              )}
            </div>

            <div className="space-y-2">
              <div className="flex items-center justify-between">
                <div>Lesson Booking</div>
                <Switch
                  checked={planData.advance_booking_enabled.lesson}
                  onChange={() => {
                    const newValue = !planData.advance_booking_enabled.lesson;
                    console.log("Toggling lesson booking enabled:", newValue);
                    setPlanData((prev) => ({
                      ...prev,
                      advance_booking_enabled: {
                        ...prev.advance_booking_enabled,
                        lesson: newValue,
                      },
                    }));
                  }}
                  className={`${
                    planData.advance_booking_enabled.lesson
                      ? "bg-blue-500"
                      : "bg-gray-200"
                  } relative mr-2 inline-flex h-6 w-11 items-center rounded-full transition-colors`}
                >
                  <span
                    className={`${
                      planData.advance_booking_enabled.lesson
                        ? "translate-x-6"
                        : "translate-x-1"
                    } inline-block h-4 w-4 transform rounded-full bg-white transition`}
                  />
                </Switch>
              </div>
              {planData.advance_booking_enabled.lesson && (
                <div className="flex items-center justify-between pl-4">
                  <div className="text-sm text-gray-500">Days in advance:</div>
                  <input
                    type="number"
                    min="1"
                    max="365"
                    value={planData.advance_booking_days.lesson}
                    onChange={(e) =>
                      setPlanData((prev) => ({
                        ...prev,
                        advance_booking_days: {
                          ...prev.advance_booking_days,
                          lesson: parseInt(e.target.value) || 1,
                        },
                      }))
                    }
                    className="w-20 rounded-md border border-gray-300 px-3 py-2 text-sm focus:border-blue-500 focus:outline-none focus:ring-1 focus:ring-blue-500"
                    required
                  />
                </div>
              )}
            </div>

            <div className="space-y-2">
              <div className="flex items-center justify-between">
                <div>Clinic/Program Booking</div>
                <Switch
                  checked={planData.advance_booking_enabled.clinic}
                  onChange={() => {
                    const newValue = !planData.advance_booking_enabled.clinic;
                    console.log("Toggling clinic booking enabled:", newValue);
                    setPlanData((prev) => ({
                      ...prev,
                      advance_booking_enabled: {
                        ...prev.advance_booking_enabled,
                        clinic: newValue,
                      },
                    }));
                  }}
                  className={`${
                    planData.advance_booking_enabled.clinic
                      ? "bg-blue-500"
                      : "bg-gray-200"
                  } relative mr-2 inline-flex h-6 w-11 items-center rounded-full transition-colors`}
                >
                  <span
                    className={`${
                      planData.advance_booking_enabled.clinic
                        ? "translate-x-6"
                        : "translate-x-1"
                    } inline-block h-4 w-4 transform rounded-full bg-white transition`}
                  />
                </Switch>
              </div>
              {planData.advance_booking_enabled.clinic && (
                <div className="flex items-center justify-between pl-4">
                  <div className="text-sm text-gray-500">Days in advance:</div>
                  <input
                    type="number"
                    min="1"
                    max="365"
                    value={planData.advance_booking_days.clinic}
                    onChange={(e) =>
                      setPlanData((prev) => ({
                        ...prev,
                        advance_booking_days: {
                          ...prev.advance_booking_days,
                          clinic: parseInt(e.target.value) || 1,
                        },
                      }))
                    }
                    className="w-20 rounded-md border border-gray-300 px-3 py-2 text-sm focus:border-blue-500 focus:outline-none focus:ring-1 focus:ring-blue-500"
                    required
                  />
                </div>
              )}
            </div>

            <div className="space-y-2">
              <div className="flex items-center justify-between">
                <div>Find-a-Buddy Booking</div>
                <Switch
                  checked={planData.advance_booking_enabled.buddy}
                  onChange={() => {
                    const newValue = !planData.advance_booking_enabled.buddy;
                    console.log("Toggling buddy booking enabled:", newValue);
                    setPlanData((prev) => ({
                      ...prev,
                      advance_booking_enabled: {
                        ...prev.advance_booking_enabled,
                        buddy: newValue,
                      },
                    }));
                  }}
                  className={`${
                    planData.advance_booking_enabled.buddy
                      ? "bg-blue-500"
                      : "bg-gray-200"
                  } relative mr-2 inline-flex h-6 w-11 items-center rounded-full transition-colors`}
                >
                  <span
                    className={`${
                      planData.advance_booking_enabled.buddy
                        ? "translate-x-6"
                        : "translate-x-1"
                    } inline-block h-4 w-4 transform rounded-full bg-white transition`}
                  />
                </Switch>
              </div>
              {planData.advance_booking_enabled.buddy && (
                <div className="flex items-center justify-between pl-4">
                  <div className="text-sm text-gray-500">Days in advance:</div>
                  <input
                    type="number"
                    min="1"
                    max="365"
                    value={planData.advance_booking_days.buddy}
                    onChange={(e) =>
                      setPlanData((prev) => ({
                        ...prev,
                        advance_booking_days: {
                          ...prev.advance_booking_days,
                          buddy: parseInt(e.target.value) || 1,
                        },
                      }))
                    }
                    className="w-20 rounded-md border border-gray-300 px-3 py-2 text-sm focus:border-blue-500 focus:outline-none focus:ring-1 focus:ring-blue-500"
                    required
                  />
                </div>
              )}
            </div>
          </div>
        </div>

        {/* Features Section */}
        <div className="space-y-4 pt-4">
          <div className="text-sm text-gray-600">Plan features</div>
          <div className="space-y-4">
            {planData.features.map((feature) => (
              <div key={feature.id} className="space-y-2">
                <div className="flex items-center justify-between">
                  <div className="text-sm text-gray-600">
                    Feature {feature.id}
                  </div>
                  <div className="space-x-4">
                    <button
                      className="text-sm text-red-600"
                      onClick={() => handleFeatureDelete(feature.id)}
                    >
                      Delete
                    </button>
                    <button
                      className="text-sm text-blue-600"
                      onClick={() =>
                        setIsEditing((prev) => ({
                          ...prev,
                          features: new Set([...prev.features, feature.id]),
                        }))
                      }
                    >
                      Edit
                    </button>
                  </div>
                </div>
                {isEditing.features.has(feature.id) ? (
                  <input
                    type="text"
                    value={feature.text}
                    onChange={(e) =>
                      setPlanData((prev) => ({
                        ...prev,
                        features: prev.features.map((f) =>
                          f.id === feature.id
                            ? { ...f, text: e.target.value }
                            : f
                        ),
                      }))
                    }
                    onBlur={() => handleFeatureEdit(feature.id, feature.text)}
                    className="w-full rounded-md border border-gray-300 px-3 py-2"
                    autoFocus
                  />
                ) : (
                  <div>{feature.text}</div>
                )}
              </div>
            ))}
            <button
              className="text-sm text-blue-600"
              onClick={handleAddFeature}
            >
              + Add feature
            </button>
          </div>
        </div>
      </div>

      <div className="flex  flex-shrink-0 justify-between gap-4 border-t border-gray-200 px-4 py-4">
        <div className="flex gap-2">
          {mode === "edit" && onDelete && (
            <button
              type="button"
              className="rounded-xl border border-red-200 bg-red-50 px-3 py-2 text-sm font-semibold text-red-600 hover:bg-red-100"
              onClick={() => onDelete(initialData)}
            >
              Delete Plan
            </button>
          )}
        </div>
        <div className="flex gap-2">
          <button
            type="button"
            className="rounded-xl border border-gray-200 px-3 py-2 text-sm font-semibold text-gray-900 hover:bg-gray-50"
            onClick={onClose}
          >
            Cancel
          </button>
          <InteractiveButton
            loading={isSubmitting}
            type="submit"
            className="rounded-xl bg-primaryBlue px-3 py-2 text-sm font-semibold text-white hover:bg-blue-700"
            onClick={handleSave}
          >
            {mode == "edit" ? "Save changes" : "Create plan"}
          </InteractiveButton>
        </div>
      </div>
    </div>
  );
}
