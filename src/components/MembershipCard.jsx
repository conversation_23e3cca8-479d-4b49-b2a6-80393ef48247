import { fCurrency } from "Utils/formatNumber";
const MembershipCard = ({
  price,
  isCurrentPlan,
  features,
  onSelect,
  popular,
  plan_name,
  isActive,
  advance_booking_days,
}) => (
  <div
    className={`h-fit rounded-xl border border-gray-200 bg-white p-5 shadow-5 ${
      isActive ? "border-primaryBlue" : ""
    }`}
  >
    <div className=" mb-3 flex items-center justify-between border-b border-gray-100 pb-4">
      <h3 className="text-lg font-semibold">{plan_name}</h3>
      {isActive && (
        <span className="rounded-lg bg-primaryBlue px-3 py-1 text-sm text-white">
          Active
        </span>
      )}
    </div>
    <div className="mb-4 text-2xl font-bold">
      {price === 0 ? (
        <span className="text-green-600">Free</span>
      ) : (
        <>
          {fCurrency(price)}
          <span className="text-sm font-normal text-gray-500">/month</span>
        </>
      )}
    </div>
    {/* Advance Booking Days Section */}
    <div className="mb-4 rounded-lg bg-gray-50 p-3">
      <h6 className="mb-2 font-medium">Advance Booking Days:</h6>
      <div className="grid grid-cols-2 gap-2 text-sm">
        <div>Court: {advance_booking_days?.court || 10} days</div>
        <div>Lesson: {advance_booking_days?.lesson || 10} days</div>
        <div>Clinic: {advance_booking_days?.clinic || 10} days</div>
        <div>Buddy: {advance_booking_days?.buddy || 10} days</div>
      </div>
    </div>

    <div className="mb-4 space-y-5">
      {features &&
        features.map((feature, index) => (
          <div key={index} className="flex items-center gap-2">
            <svg
              width="22"
              height="22"
              viewBox="0 0 22 22"
              fill="none"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                d="M5.50065 4.54704C7.32378 6.10228 8.47982 8.41646 8.47982 11.0007C8.47982 13.5848 7.32378 15.899 5.50065 17.4543M5.50065 4.54704C3.67752 6.10228 2.52148 8.41646 2.52148 11.0007C2.52148 13.5848 3.67752 15.899 5.50065 17.4543M5.50065 4.54704C6.98129 3.28397 8.90193 2.52148 11.0007 2.52148C13.0994 2.52148 15.02 3.28397 16.5007 4.54704M5.50065 17.4543C6.98129 18.7173 8.90193 19.4798 11.0007 19.4798C13.0994 19.4798 15.02 18.7173 16.5007 17.4543M16.5007 4.54704C14.6775 6.10228 13.5215 8.41646 13.5215 11.0007C13.5215 13.5848 14.6775 15.899 16.5007 17.4543M16.5007 4.54704C18.3238 6.10228 19.4798 8.41646 19.4798 11.0007C19.4798 13.5848 18.3238 15.899 16.5007 17.4543"
                stroke="#525866"
                strokeWidth="1.5"
              />
            </svg>

            <span>{feature.text}</span>
          </div>
        ))}
    </div>
    <button
      onClick={onSelect}
      className={`w-full rounded-lg py-2 ${
        isActive
          ? "cursor-not-allowed bg-gray-100 text-gray-400"
          : "border border-primaryBlue text-primaryBlue hover:bg-primaryBlue hover:text-white"
      }`}
      disabled={isActive}
    >
      {isActive ? "Active" : "Select"}
    </button>
  </div>
);

export default MembershipCard;
