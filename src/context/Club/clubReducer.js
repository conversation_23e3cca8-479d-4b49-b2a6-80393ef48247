/**
 * Club Reducer and Action Types
 * Manages state for the Club context
 */

// Action types as constants for better maintainability
export const CLUB_ACTIONS = {
  SET_LOADING: "SET_LOADING",
  SET_CLUB: "SET_CLUB",
  SET_SPORTS: "SET_SPORTS",
  SET_PRICING: "SET_PRICING",
  SET_COURTS: "SET_COURTS",
  SET_COACH_PROFILE: "SET_COACH_PROFILE",
  SET_STAFF_PROFILE: "SET_STAFF_PROFILE",
  SET_USER_PROFILE: "SET_USER_PROFILE",
  SET_ERROR: "SET_ERROR",
  SET_CLUB_MEMBERSHIP: "SET_CLUB_MEMBERSHIP",
  SET_USER_SUBSCRIPTION: "SET_USER_SUBSCRIPTION",
  SET_USER_PERMISSIONS: "SET_USER_PERMISSIONS",
  SET_CLUB_PERMISSIONS: "SET_CLUB_PERMISSIONS",
  TRIGGER_REFETCH: "TRIGGER_REFETCH",
};

// Initial state for the club context
export const initialState = {
  club: null,
  sports: [],
  courts: [],
  coach_profile: null,
  user_profile: null,
  staff_profile: null,
  staff_access: null,
  club_membership: [],
  user_subscription: null,
  user_permissions: null,
  loading: false,
  error: null,
  pricing: [],
  club_permissions: null,
  refetchTrigger: 0,
};

/**
 * Club reducer function
 * @param {Object} state - Current state
 * @param {Object} action - Action with type and payload
 * @returns {Object} New state
 */
export const clubReducer = (state, action) => {
  switch (action.type) {
    case CLUB_ACTIONS.SET_LOADING:
      return {
        ...state,
        loading: action.payload,
      };
    case CLUB_ACTIONS.SET_CLUB:
      return {
        ...state,
        club: action.payload,
        loading: false,
      };
    case CLUB_ACTIONS.SET_SPORTS:
      return {
        ...state,
        sports: action.payload,
        loading: false,
      };
    case CLUB_ACTIONS.SET_PRICING:
      return {
        ...state,
        pricing: action.payload,
        loading: false,
      };
    case CLUB_ACTIONS.SET_COURTS:
      return {
        ...state,
        courts: action.payload,
        loading: false,
      };
    case CLUB_ACTIONS.SET_COACH_PROFILE:
      return {
        ...state,
        coach_profile: action.payload,
        loading: false,
      };
    case CLUB_ACTIONS.SET_STAFF_PROFILE:
      return {
        ...state,
        staff_profile: action.payload,
        loading: false,
      };
    case CLUB_ACTIONS.SET_USER_PROFILE:
      return {
        ...state,
        user_profile: action.payload,
        loading: false,
      };
    case CLUB_ACTIONS.SET_ERROR:
      return {
        ...state,
        error: action.payload,
        loading: false,
      };
    case CLUB_ACTIONS.SET_CLUB_MEMBERSHIP:
      return {
        ...state,
        club_membership: action.payload,
      };
    case CLUB_ACTIONS.SET_USER_SUBSCRIPTION:
      return {
        ...state,
        user_subscription: action.payload,
      };
    case CLUB_ACTIONS.SET_USER_PERMISSIONS:
      return {
        ...state,
        user_permissions: action.payload,
      };
    case CLUB_ACTIONS.SET_CLUB_PERMISSIONS:
      return {
        ...state,
        club_permissions: action.payload,
        staff_access: action.payload,
      };
    case CLUB_ACTIONS.TRIGGER_REFETCH:
      return {
        ...state,
        refetchTrigger: state.refetchTrigger + 1,
      };
    default:
      return state;
  }
};
