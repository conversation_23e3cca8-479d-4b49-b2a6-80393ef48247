/**
 * Club Context Constants
 * Centralized constants for the Club context
 */

// User roles
export const USER_ROLES = {
  USER: "user",
  COACH: "coach",
  CLUB: "club",
  STAFF: "staff",
  ADMIN: "admin",
};

// API endpoints
export const API_ENDPOINTS = {
  USER_SUBSCRIPTION: (userId) => `/v3/api/custom/courtmatchup/user/subscription/${userId}`,
  COACH_PROFILE: "/v3/api/custom/courtmatchup/coach/profile",
  STAFF_PROFILE: "/v3/api/custom/courtmatchup/staff/profile",
  CLUB_PROFILE: "/v3/api/custom/courtmatchup/club/profile",
  USER_CLUB: (clubId) => `/v3/api/custom/courtmatchup/user/club/${clubId}`,
  ROLE_ACCESS: (clubId) => `/v3/api/custom/courtmatchup/role-access/${clubId}`,
};

// Error messages
export const ERROR_MESSAGES = {
  FETCH_SUBSCRIPTION: "Failed to fetch user subscription",
  FETCH_PROFILE: "Failed to fetch user profile",
  FETCH_CLUB_DATA: "Failed to fetch club data",
  PARSE_MEMBERSHIP: "Failed to parse membership settings",
  SET_PERMISSIONS: "Failed to set user permissions",
  AUTHENTICATION_REQUIRED: "Authentication required",
  INVALID_ROLE: "Invalid user role",
};

// Default values
export const DEFAULTS = {
  MEMBERSHIP_SETTINGS: [],
  SPORTS: [],
  COURTS: [],
  PRICING: [],
};

// Membership plan properties
export const MEMBERSHIP_PLAN_FIELDS = {
  PLAN_ID: "plan_id",
  PLAN_NAME: "plan_name",
  PRICE: "price",
  ALLOW_CLINIC: "allow_clinic",
  ALLOW_BUDDY: "allow_buddy",
  ALLOW_COACH: "allow_coach",
  ALLOW_GROUPS: "allow_groups",
  ALLOW_COURT: "allow_court",
  FEATURES: "features",
  APPLICABLE_SPORTS: "applicable_sports",
  ADVANCE_BOOKING_DAYS: "advance_booking_days",
  ADVANCE_BOOKING_ENABLED: "advance_booking_enabled",
};

// Navigation routes
export const ROUTES = {
  MEMBERSHIP_BUY: "/user/membership/buy",
};
